// ==UserScript==
// @name         小梅花智能客服 v9.0.23 8-26-优化3
// @namespace    http://tampermonkey.net/
// @version      9.0.23
// @description  【本次优化】1.【新增】全自动会话转接功能，可根据关键词自动转接给指定客服。2.【优化】重构UI，将“自动结束对话”设置整合到“会话监控”中，移除独立的“自动化设置”模块。3.【优化】隐藏“跳过系统回复”开关，并默认开启。
// @description  【v9.0.23更新】完全恢复AI智能回复设置、启用AI回复、API密钥、模型、深度思考(R1)、回复延迟、AI状态显示等AI功能，与参考代码功能保持一致。
// @description  【功能精简v9.0.19】彻底删除所有Excel相关功能：关键词回复模板、AI产品知识学习、AI知识训练学习、产品信息介绍、AI禁止回复知识学习、商品种类学习等，仅保留AI智能体知识库。
// @description  【反调试增强v9.0.17】终极反调试保护系统：检测到开发者工具瞬间触发空白页，完全清空页面内容和控制台，阻止任何代码在开发者工具中显示，支持所有平台和浏览器，包括快捷键、右键菜单、控制台访问等全方位防护。
// <AUTHOR>
// @match        https://store.weixin.qq.com/shop/kf*
// @match        https://store.weixin.qq.com/shop/home*
// @match        https://store.weixin.qq.com/shop/order/list*
// @match        https://xiaomeihuakefu.cn/ai-knowledge.html*
// @match        ai-knowledge.html*
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_deleteValue
// @grant        GM_xmlhttpRequest
// @grant        GM_openInTab
// @grant        window.close
// @require      https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js
// @require      https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js
// @connect      api.deepseek.com
// @connect      store.weixin.qq.com
// @connect      *
// @icon         https://res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.ico
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // =================================================================
    // ========== 强化反调试和开发者工具检测功能 ==========
    // =================================================================

    // 检测开发者工具的多种方法
    let devToolsOpen = false;
    let devToolsDetectionCount = 0; // 检测计数器，避免误报
    let lastDetectionTime = 0;
    let isLegitimateUser = false; // 标记是否为合法用户
    let antiDebugActive = true; // 反调试功能开关
    let blankPageTriggered = false; // 是否已触发空白页

    // 检测是否为合法用户（例如：正在使用脚本功能）
    function checkLegitimateUser() {
        // 检查是否有脚本面板打开
        const panel = document.getElementById('autoReplyPanel');
        if (panel && panel.style.display !== 'none') {
            isLegitimateUser = true;
            return true;
        }

        // 检查是否在使用脚本快捷键
        const now = Date.now();
        if (now - lastDetectionTime < 5000) { // 5秒内的检测可能是误报
            return true;
        }

        return false;
    }

    // 检测操作系统类型
    function isMacOS() {
        return navigator.platform.toUpperCase().indexOf('MAC') >= 0 ||
               navigator.userAgent.toUpperCase().indexOf('MAC') >= 0;
    }

    // 方法1: 检测控制台大小变化（超敏感版）
    function detectDevToolsBySize() {
        const threshold = 100; // 极低阈值，超高敏感度
        const heightDiff = window.outerHeight - window.innerHeight;
        const widthDiff = window.outerWidth - window.innerWidth;

        // 超敏感检测条件
        return (heightDiff > threshold) || (widthDiff > threshold);
    }

    // 方法2: 检测调试器断点（超敏感版）
    function detectDebugger() {
        const start = Date.now();
        debugger;
        const end = Date.now();
        return end - start > 10; // 极低阈值，任何暂停都会被检测
    }

    // 方法3: 检测控制台对象（超敏感版）
    function detectConsole() {
        let devtools = {open: false, orientation: null};
        const threshold = 100; // 极低阈值

        setInterval(() => {
            if (!antiDebugActive || blankPageTriggered) return;

            const heightDiff = window.outerHeight - window.innerHeight;
            const widthDiff = window.outerWidth - window.innerWidth;

            if (heightDiff > threshold || widthDiff > threshold) {
                devtools.open = true;
                devtools.orientation = widthDiff > threshold ? 'vertical' : 'horizontal';
                // 立即触发，不需要计数
                if (!devToolsOpen) {
                    devToolsOpen = true;
                    lastDetectionTime = Date.now();
                    handleDevToolsDetected();
                }
            } else {
                devtools.open = false;
                devToolsOpen = false;
                devToolsDetectionCount = 0;
            }
        }, 100); // 极高检测频率
    }

    // 方法4: 实时窗口尺寸监控
    function setupWindowSizeMonitor() {
        let lastInnerWidth = window.innerWidth;
        let lastInnerHeight = window.innerHeight;
        let lastOuterWidth = window.outerWidth;
        let lastOuterHeight = window.outerHeight;

        setInterval(() => {
            if (!antiDebugActive || blankPageTriggered) return;

            const currentInnerWidth = window.innerWidth;
            const currentInnerHeight = window.innerHeight;
            const currentOuterWidth = window.outerWidth;
            const currentOuterHeight = window.outerHeight;

            // 检测任何尺寸变化
            if (currentInnerWidth !== lastInnerWidth ||
                currentInnerHeight !== lastInnerHeight ||
                currentOuterWidth !== lastOuterWidth ||
                currentOuterHeight !== lastOuterHeight) {

                // 立即检测开发者工具
                if (detectDevToolsBySize()) {
                    handleDevToolsDetected();
                }
            }

            lastInnerWidth = currentInnerWidth;
            lastInnerHeight = currentInnerHeight;
            lastOuterWidth = currentOuterWidth;
            lastOuterHeight = currentOuterHeight;
        }, 50); // 极高频率监控
    }

    // 方法4: 检测控制台访问
    function detectConsoleAccess() {
        let devtools = false;
        let element = new Image();
        Object.defineProperty(element, 'id', {
            get: function() {
                devtools = true;
                if (!blankPageTriggered) {
                    handleDevToolsDetected();
                }
            }
        });

        setInterval(() => {
            if (!antiDebugActive || blankPageTriggered) return;
            devtools = false;
            console.log(element);
            console.clear();
        }, 800);
    }

    // 方法5: 检测开发者工具面板
    function detectDevToolsPanel() {
        const devtools = {
            open: false,
            orientation: null
        };

        const threshold = 160;

        setInterval(() => {
            if (!antiDebugActive || blankPageTriggered) return;

            const heightDiff = window.outerHeight - window.innerHeight;
            const widthDiff = window.outerWidth - window.innerWidth;

            if (heightDiff > threshold || widthDiff > threshold) {
                if (!devtools.open) {
                    devtools.open = true;
                    devtools.orientation = widthDiff > threshold ? 'vertical' : 'horizontal';
                    handleDevToolsDetected();
                }
            } else {
                devtools.open = false;
            }
        }, 300);
    }

    // 方法6: 智能快捷键处理（Mac兼容）
    function setupKeyboardProtection() {
        // 禁用右键菜单
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }, true);

        // 智能快捷键处理
        document.addEventListener('keydown', function(e) {
            const isMac = isMacOS();

            // F12 - 在所有平台都禁用
            if (e.key === 'F12') {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }

            // Windows/Linux 快捷键禁用
            if (!isMac) {
                // Ctrl+Shift+I (开发者工具)
                if (e.ctrlKey && e.shiftKey && e.key === 'I') {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }

                // Ctrl+Shift+C (元素选择器)
                if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }

                // Ctrl+Shift+J (控制台)
                if (e.ctrlKey && e.shiftKey && e.key === 'J') {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }

                // Ctrl+U (查看源代码)
                if (e.ctrlKey && e.key === 'u') {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
            }
            // Mac 用户保留快捷键功能，不阻止

            // 通用禁用
            // Ctrl+S (保存页面)
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }

            // Ctrl+A (全选) - 在某些情况下防止复制页面内容
            if (e.ctrlKey && e.key === 'a') {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }

            // Ctrl+P (打印) - 防止打印页面查看源码
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        }, true);
    }

    // 处理检测到开发者工具的情况（终极版 - 彻底清空）
    function handleDevToolsDetected() {
        if (blankPageTriggered) return; // 避免重复触发

        blankPageTriggered = true;
        antiDebugActive = false; // 停止检测

        // 立即彻底清空页面并阻止任何代码显示
        triggerUltimateBlankPage();
    }

    // 触发终极空白页面 - 彻底清空所有内容
    function triggerUltimateBlankPage() {
        try {
            // 1. 立即停止所有脚本执行
            window.stop && window.stop();

            // 2. 清空所有定时器
            const highestTimeoutId = setTimeout(() => {}, 0);
            for (let i = 0; i < highestTimeoutId; i++) {
                clearTimeout(i);
                clearInterval(i);
            }

            // 3. 移除所有事件监听器
            const newDocument = document.implementation.createHTMLDocument('');

            // 4. 彻底清空当前文档
            document.documentElement.innerHTML = '';
            document.head.innerHTML = '';
            document.body.innerHTML = '';

            // 5. 重写整个页面为空白
            const blankHTML = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>页面加载中</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        html, body {
            height: 100%;
            background: #fff;
            font-family: system-ui, -apple-system, sans-serif;
            overflow: hidden;
        }
        .container {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #666;
        }
        .spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .text { font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <div class="text">页面加载中...</div>
    </div>
    <script>
        // 阻止任何调试行为
        (function() {
            'use strict';

            // 禁用所有快捷键
            document.addEventListener('keydown', function(e) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }, true);

            // 禁用右键
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }, true);

            // 持续检测并保持空白
            setInterval(function() {
                if (document.body.children.length > 1) {
                    document.body.innerHTML = '<div class="container"><div class="spinner"></div><div class="text">页面加载中...</div></div>';
                }

                // 清空控制台
                if (typeof console !== 'undefined') {
                    try {
                        console.clear();
                        console.log = function() {};
                        console.warn = function() {};
                        console.error = function() {};
                        console.info = function() {};
                        console.debug = function() {};
                    } catch(e) {}
                }
            }, 100);

            // 阻止页面被修改
            Object.defineProperty(document, 'body', {
                get: function() { return document.getElementsByTagName('body')[0]; },
                set: function() { return false; },
                configurable: false
            });

        })();
    </script>
</body>
</html>`;

            // 6. 完全重写文档
            document.open();
            document.write(blankHTML);
            document.close();

            // 7. 额外保护 - 持续清空
            const protectionInterval = setInterval(() => {
                try {
                    // 确保页面保持空白
                    if (document.documentElement.children.length > 2) {
                        document.documentElement.innerHTML = blankHTML.match(/<html>([\s\S]*)<\/html>/)[1];
                    }

                    // 清空控制台
                    if (typeof console !== 'undefined') {
                        console.clear();
                        console.log = () => {};
                        console.warn = () => {};
                        console.error = () => {};
                        console.info = () => {};
                        console.debug = () => {};
                        console.table = () => {};
                        console.trace = () => {};
                    }

                    // 阻止任何脚本注入
                    const scripts = document.querySelectorAll('script');
                    scripts.forEach((script, index) => {
                        if (index > 0) { // 保留第一个保护脚本
                            script.remove();
                        }
                    });

                } catch(e) {
                    // 忽略错误，继续保护
                }
            }, 50);

        } catch(e) {
            // 如果上述方法失败，使用备用方案
            document.documentElement.innerHTML = '<body style="background:#fff;height:100vh;display:flex;align-items:center;justify-content:center;font-family:system-ui;color:#666;"><div>页面加载中...</div></body>';
        }
    }

    // 初始化终极反调试功能
    function initAntiDebug() {
        if (!antiDebugActive) return;

        // 启动所有检测方法
        setupKeyboardProtection();
        detectConsole();
        detectConsoleAccess();
        detectDevToolsPanel();
        setupWindowSizeMonitor();

        // 超高频检测开发者工具
        setInterval(() => {
            if (!antiDebugActive || blankPageTriggered) return;

            if (detectDevToolsBySize() || detectDebugger()) {
                handleDevToolsDetected();
            }
        }, 50); // 超高频检测

        // 立即检测当前状态
        setTimeout(() => {
            if (detectDevToolsBySize()) {
                handleDevToolsDetected();
            }
        }, 100);

        // 检测页面失焦（立即检测）
        window.addEventListener('blur', () => {
            if (!antiDebugActive || blankPageTriggered) return;
            // 失焦立即检测
            setTimeout(() => {
                if (detectDevToolsBySize()) {
                    handleDevToolsDetected();
                }
            }, 50);
        });

        window.addEventListener('focus', () => {
            if (!antiDebugActive || blankPageTriggered) return;
            // 重新获得焦点时立即检测
            setTimeout(() => {
                if (detectDevToolsBySize()) {
                    handleDevToolsDetected();
                }
            }, 50);
        });

        // 检测窗口大小变化（立即检测）
        window.addEventListener('resize', () => {
            if (!antiDebugActive || blankPageTriggered) return;
            // 立即检测，不延迟
            if (detectDevToolsBySize()) {
                handleDevToolsDetected();
            }
        });

        // 检测开发者工具快捷键（立即检测）
        document.addEventListener('keydown', function(e) {
            if (!antiDebugActive || blankPageTriggered) return;

            // 检测常见的开发者工具快捷键
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'C' || e.key === 'J')) ||
                (e.metaKey && e.altKey && e.key === 'I')) { // Mac 快捷键

                // 立即检测
                setTimeout(() => {
                    if (detectDevToolsBySize()) {
                        handleDevToolsDetected();
                    }
                }, 100);

                // 持续检测一段时间
                let checkCount = 0;
                const checkInterval = setInterval(() => {
                    if (checkCount++ > 20 || blankPageTriggered) {
                        clearInterval(checkInterval);
                        return;
                    }
                    if (detectDevToolsBySize()) {
                        clearInterval(checkInterval);
                        handleDevToolsDetected();
                    }
                }, 100);
            }
        }, true);

        // 额外的保护层 - 监控DOM变化
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(() => {
                if (!antiDebugActive || blankPageTriggered) return;
                // DOM变化时检测
                setTimeout(() => {
                    if (detectDevToolsBySize()) {
                        handleDevToolsDetected();
                    }
                }, 50);
            });

            observer.observe(document.documentElement, {
                childList: true,
                subtree: true,
                attributes: true
            });
        }
    }

    // 立即启动强化反调试功能
    initAntiDebug();

    // 添加全局样式
    GM_addStyle(`
    /* ================================================= */
    /* ========== UI优化：横向A4纸布局样式 ========== */
    /* ================================================= */

    /* 控制面板样式 - A4布局 */
    .auto-reply-panel {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10000;
        background: #fdfdff;
        border-radius: 16px;
        box-shadow: 0 12px 60px rgba(0, 0, 0, 0.25);
        padding: 0; /* Padding由内部元素控制 */

        /* A4纸张尺寸的响应式设计 */
        width: 1188px;
        /* height: 842px; */ /*【优化】移除固定高度，实现自适应 */
        max-width: 95vw;
        max-height: 95vh;

        font-family: -apple-system, BlinkMacSystemFont, "Segue UI", Roboto, Helvetica, Arial, sans-serif;
        transition: all 0.3s ease;
        border: 1px solid #e0e6f0;
        display: flex; /* 使用Flexbox进行主布局 */
        flex-direction: column;
        overflow-y: auto; /*【优化】让整个面板可以垂直滚动 */
        overflow-x: visible; /* 允许按钮显示在面板外部 */
    }

    /* JS会控制其显示，默认为none防止加载时闪烁 */
    .auto-reply-panel {
        display: none;
    }

    /* 面板包装器 - 为收起按钮提供相对定位上下文 */
    .panel-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: visible; /* 允许按钮显示在面板外部 */
    }

    /* 新增：主内容区域Flex容器 */
    .panel-main-content {
        display: flex;
        flex-direction: row;
        gap: 20px;
        padding: 20px;
        flex-grow: 1;
        /* overflow: hidden; */ /*【优化】移除，允许内容撑开面板 */
        background-color: #f4f7fa;
    }

    /* 新增：列样式 */
    .panel-column {
        display: flex;
        flex-direction: column;
        gap: 15px;
        flex: 1;
        /* overflow-y: auto; */ /*【优化】移除独立列滚动，改为整体滚动 */
        padding: 8px;
        background-color: transparent;
        border-radius: 8px;
        min-width: 0; /* Flexbox收缩修正 */
    }

    /* 调整列内元素的边距 */
    .collapsible-group, .stats-section, .btn-container {
        margin-bottom: 0; /* 使用列的gap属性来控制间距 */
    }

    .btn-container {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        background: white;
        padding: 15px;
        border-radius: 10px;
        border: 1px solid #e1e8f0;
    }

    /* 确保商品设置部分能填满列的高度 */
    #group6 {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        min-height: 0;
    }
    #group6 .group-content {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        min-height: 0;
    }
    .shop-products-section {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        min-height: 0;
    }

    /* ================================================= */
    /* ========== 新增：控制面板收起/展开功能 ========== */
    /* ================================================= */
    .collapse-trigger {
        position: fixed; /* 固定定位，完全独立于面板 */
        /* 精确计算：控制面板宽度1188px + 2px边框，居中定位，右边缘在 50% + 595px */
        /* 完全紧贴控制面板右边外侧（包括边框） */
        left: calc(50% + 595px); /* 紧贴控制面板右边外侧（含边框） */
        top: 50%; /* 垂直居中 */
        transform: translateY(-50%);
        width: 40px; /* 合适的宽度 */
        height: 120px; /* 合适的高度 */
        padding: 8px 4px; /* 合适的内边距 */
        background: linear-gradient(135deg, #868f96 0%, #596164 100%);
        color: white !important; /* 强制白色文字 */
        border-radius: 8px; /* 全圆角设计 */
        box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15);
        cursor: pointer;
        font-size: 14px !important; /* 合适的字体大小 */
        font-weight: 500;
        text-align: center;
        border: none;
        transition: all 0.3s ease; /* 增加过渡时间 */
        z-index: 10002;
        writing-mode: vertical-rl; /* 竖排文字 */
        text-orientation: mixed;
        display: flex;
        align-items: center;
        justify-content: center;
        letter-spacing: 2px; /* 合适的字间距 */
        border: 1px solid rgba(255, 255, 255, 0.2);
        line-height: 1.4 !important; /* 合适的行高 */
        white-space: nowrap; /* 防止文字换行 */
        overflow: visible; /* 确保文字可见 */
        text-overflow: visible; /* 确保文字不被截断 */
    }

    /* 移除悬停效果 */

    /* 响应式设计：当屏幕宽度小于控制面板宽度时，按钮回退到屏幕右边缘 */
    @media (max-width: 1300px) {
        .collapse-trigger {
            right: 20px; /* 回退到屏幕右边缘 */
            left: auto; /* 取消left定位 */
        }
        /* 移除响应式悬停效果 */
    }

    /* 【修复】AI训练对话框响应式设计 - 只在真正的小屏幕上生效 */
    @media (max-width: 1250px) {
        .ai-training-dialog {
            width: 95vw !important; /* 在小屏幕上使用视窗宽度 */
            height: auto !important; /* 在小屏幕上允许自适应高度 */
            max-width: 95vw;
            margin: 0 2.5vw; /* 左右留出一些边距 */
        }

        .ai-training-messages {
            padding: 20px; /* 减少内边距 */
        }

        .ai-training-input-area {
            padding: 20px; /* 减少内边距 */
        }

        .ai-training-msg {
            max-width: 85%; /* 在小屏幕上增加消息宽度比例 */
        }
    }

    /* 【新增】更小屏幕的响应式设计 */
    @media (max-width: 768px) {
        .ai-training-dialog {
            width: 98vw;
            max-width: 98vw;
            margin: 0 1vw;
            border-radius: 12px; /* 减少圆角 */
        }

        .ai-training-dialog-header {
            padding: 15px 20px; /* 减少头部内边距 */
            font-size: 16px; /* 减少字体大小 */
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
        }

        .ai-training-messages {
            padding: 15px; /* 进一步减少内边距 */
        }

        .ai-training-input-area {
            padding: 15px; /* 进一步减少内边距 */
        }

        .ai-training-msg {
            max-width: 90%; /* 在移动设备上进一步增加消息宽度比例 */
        }

        .ai-training-msg .content {
            padding: 12px 16px; /* 减少消息气泡内边距 */
            font-size: 13px; /* 稍微减少字体大小 */
        }

        .ai-training-input-area textarea {
            height: 45px; /* 减少输入框高度 */
            font-size: 13px; /* 减少字体大小 */
        }

        .upload-file-btn, #sendTrainingMsgBtn {
            width: 36px; /* 减少按钮大小 */
            height: 36px;
            font-size: 14px; /* 减少图标大小 */
        }
    }

    .collapsed-thumbnail {
        display: none; /* Hidden by default */
        position: fixed;
        bottom: 25px;
        right: 25px;
        width: 400px; /* 优化：新尺寸 */
        height: 200px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* 优化：新渐变色 */
        border-radius: 16px;
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.35);
        z-index: 10001; /* Must be on top when panel is hidden */
        cursor: pointer;
        color: white;
        padding: 20px;
        display: flex; /* Changed from 'none' to 'flex' for alignment */
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .collapsed-thumbnail:hover {
        transform: translateY(-5px) scale(1.03);
        box-shadow: 0 18px 50px rgba(0, 0, 0, 0.4);
    }
    .thumbnail-title {
        font-size: 28px; /* 优化：字体微调 */
        font-weight: 600;
        margin-bottom: 15px;
        text-shadow: 1px 1px 4px rgba(0,0,0,0.3);
    }
    .thumbnail-subtitle {
        font-size: 20px; /* 优化：字体微调 */
        font-weight: 400;
        opacity: 0.9;
        line-height: 1.4;
        text-shadow: 1px 1px 4px rgba(0,0,0,0.2);
    }

    /* ================================================= */
    /* ========== END OF UI OPTIMIZATION STYLES ========== */
    /* ================================================= */

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-on {
        background-color: #07C160;
        box-shadow: 0 0 8px rgba(7, 193, 96, 0.5);
    }

    .status-off {
        background-color: #FA5151;
    }

    .control-btn {
        flex: 1;
        padding: 10px 12px;
        border-radius: 8px;
        border: none;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.25s;
        text-align: center;
        min-width: 90px;
        font-size: 13px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    /* 按钮禁用样式 */
    .control-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .btn-enable {
        background: linear-gradient(135deg, #09e85e 0%, #07C160 100%);
        color: white;
    }

    .btn-disable {
        background: linear-gradient(135deg, #ff7a7a 0%, #FA5151 100%);
        color: white;
    }

    .btn-upload {
        background: linear-gradient(135deg, #4facfe 0%, #10AEFF 100%);
        color: white;
    }

    .btn-download {
        background: linear-gradient(135deg, #a18cd1 0%, #8a6dff 100%);
        color: white;
    }

    .btn-refresh {
        background: linear-gradient(135deg, #ffd26f 0%, #ffb26b 100%);
        color: white;
    }
     /* 新增AI知识库按钮样式 */
    .btn-ai-train {
        background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
        color: white;
    }
    .btn-ai-clear {
        background: linear-gradient(135deg, #98a1a9 0%, #6c757d 100%);
        color: white;
    }
    /* 【新增】客服反馈功能按钮样式 */
    .btn-feedback-download {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }
    .btn-feedback-upload {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }
    .btn-feedback-reply {
         background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }

    /* 【新增】店铺商品设置按钮样式 */
    .btn-shop-products {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .control-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }

    .control-btn:active {
        transform: translateY(1px);
    }

    .section-title {
        font-size: 14px;
        color: #2c3e50;
        margin-bottom: 10px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .template-section {
        margin-bottom: 15px;
    }

    .template-list {
        border: 1px solid #e1e8f0;
        border-radius: 8px;
        max-height: 200px;
        overflow-y: auto;
        padding: 8px;
        background: #fafcff;
    }



    .stats-section {
        background: #ffffff;
        border-radius: 10px;
        padding: 15px;
        font-size: 13px;
        border: 1px solid #e1e8f0;
    }

    .stats-row {
        display: flex;
        align-items: center; /* 垂直居中对齐 */
        margin-bottom: 8px;
    }

    .stats-label {
        color: #5a6c80;
        width: 100px;
        font-weight: 500;
        flex-shrink: 0;
    }

    .stats-value {
        color: #2c3e50;
        font-weight: 600;
        flex: 1;
        display: flex; /* 让内部元素对齐 */
        align-items: center;
    }

    .file-input {
        display: none;
    }

    .notification {
        position: fixed;
        top: 30px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.85);
        color: white;
        padding: 14px 30px;
        border-radius: 10px;
        z-index: 10001;
        animation: fadeInOut 3s forwards;
        display: none;
        font-size: 14px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    @keyframes fadeInOut {
        0% { opacity: 0; top: 10px; }
        10% { opacity: 1; top: 30px; }
        90% { opacity: 1; top: 30px; }
        100% { opacity: 0; top: 10px; }
    }

    .empty-templates {
        text-align: center;
        color: #a3b1c2;
        padding: 20px;
        font-size: 13px;
    }

    .info-tip {
        color: #10AEFF;
        font-size: 11px;
        margin-top: 5px;
    }

    /* 【修复】AI信息提示样式 - 与参考代码保持一致 */
    .ai-info-tip {
        font-size: 12px;
        color: #5a6c80;
        margin-top: 5px;
        padding: 5px;
        background: #f8f9ff;
        border-radius: 6px;
    }

    /* 新增样式 */
    .refresh-section,
    .auto-end-section,
    .ai-section,
    .feedback-section,
    .shop-products-section { /* 【新增】 */
        margin-bottom: 15px;
    }

    /* 【功能优化】移除独立section的边框和内边距，统一由group-content控制 */
    .refresh-section, .auto-end-section, .feedback-section, .shop-products-section {
        margin-bottom: 0px;
    }
    .refresh-section:not(:last-child), .auto-end-section:not(:last-child), .feedback-section:not(:last-child), .shop-products-section:not(:last-child) {
        margin-bottom: 15px; /* 同组内元素间距 */
    }


    .refresh-input-container {
        display: flex;
        gap: 8px;
    }

    .refresh-input {
        flex: 1;
        padding: 10px;
        border-radius: 8px;
        border: 1px solid #e1e8f0;
        font-size: 13px;
    }

    .btn-save-refresh,
    .btn-stop-refresh {
        background: linear-gradient(135deg, #ffb26b 0%, #ff8c6b 100%);
        color: white;
        padding: 8px 12px;
        border-radius: 8px;
        border: none;
        cursor: pointer;
        font-size: 13px;
        min-width: 60px;
    }

    .refresh-status {
        font-size: 12px;
        color: #5a6c80;
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .refresh-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #FA5151;
    }

    .refresh-indicator.active {
        background-color: #07C160;
    }

    /* 新增跳过系统回复开关样式 */
    .switch {
        position: relative;
        display: inline-block;
        width: 40px;
        height: 20px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 20px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #07C160;
    }

    input:checked + .slider:before {
        transform: translateX(20px);
    }

    /* 【功能修复】客服名字输入框样式 */
    .cs-name-input {
        width: 100%;
        padding: 8px;
        border-radius: 8px;
        border: 1px solid #e1e8f0;
        font-size: 13px;
        background-color: #f0f4f8; /* 只读状态下的背景色 */
        color: #5a6c80; /* 只读状态下的文字颜色 */
        cursor: not-allowed; /* 显示为不可用光标 */
    }

    /* 新增结束状态样式 */
    .auto-end-status {
        font-size: 12px;
        margin-top: 5px;
        padding: 5px;
        border-radius: 4px;
        background: #f0f4f8;
    }

    .auto-end-active {
        color: #07C160;
        font-weight: 600;
    }

    .auto-end-inactive {
        color: #FA5151;
    }

    /* ========== 恢复：AI智能回复设置样式 ========== */
    .ai-section {
        display: none; /* 隐藏AI智能回复设置 */
        margin-bottom: 0;
        border-top: none;
        padding-top: 0;
    }

    .ai-settings-row {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;
        align-items: center;
    }

    .ai-toggle-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .ai-api-input {
        flex: 1;
        padding: 8px;
        border-radius: 6px;
        border: 1px solid #e1e8f0;
        font-size: 13px;
    }

    .ai-model-select {
        padding: 8px;
        border-radius: 6px;
        border: 1px solid #e1e8f0;
        font-size: 13px;
        background: white;
    }

    .ai-status {
        display: flex;
        align-items: center;
        gap: 5px;
        margin-top: 5px;
    }

    .ai-status-text {
        font-size: 12px;
        color: #5a6c80;
    }

    .ai-status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #FA5151;
    }

    .ai-status-indicator.active {
        background-color: #07C160;
    }

    /* 新增AI延迟设置样式 */
    .ai-delay-input {
        padding: 8px;
        border-radius: 6px;
        border: 1px solid #e1e8f0;
        font-size: 13px;
        width: 80px;
    }

    /* ========== 【优化2】新增：内置API密钥输入样式 ========== */
    .builtin-api-keys-container {
        width: 100%;
        margin-bottom: 15px;
    }

    .builtin-api-input {
        width: 100%;
        padding: 10px;
        border-radius: 8px;
        border: 1px solid #e1e8f0;
        font-size: 13px;
        font-family: monospace;
        resize: vertical;
        min-height: 80px;
        background: #f8f9ff;
        margin-bottom: 8px;
    }

    .builtin-api-input:focus {
        outline: none;
        border-color: #4facfe;
        box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2);
    }

    .btn-save-builtin-api {
        background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        font-size: 13px;
        min-width: 120px;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .builtin-api-info {
        font-size: 11px;
        color: #5a6c80;
        background: #f0f4f8;
        padding: 6px 8px;
        border-radius: 4px;
        border-left: 3px solid #4facfe;
    }

    /* ========== 新增：API密钥管理样式 ========== */
    .api-keys-container {
        width: 100%;
    }

    .api-key-input-row {
        display: flex;
        gap: 8px;
        margin-bottom: 10px;
        align-items: center;
    }

    .api-key-input-row .ai-api-input {
        flex: 1;
    }

    .btn-save-api {
        background: linear-gradient(135deg, #4facfe 0%, #10AEFF 100%);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        font-size: 13px;
        min-width: 60px;
        font-weight: 500;
    }

    .saved-api-keys {
        max-height: 150px;
        overflow-y: auto;
        border: 1px solid #e1e8f0;
        border-radius: 6px;
        background: #f8f9ff;
        padding: 8px;
    }

    .api-key-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 10px;
        margin-bottom: 6px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e1e8f0;
        font-size: 13px;
    }

    .api-key-item:last-child {
        margin-bottom: 0;
    }

    .api-key-display {
        flex: 1;
        font-family: monospace;
        color: #5a6c80;
        margin-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .api-key-actions {
        display: flex;
        gap: 6px;
        align-items: center;
    }

    .btn-delete-api {
        background: linear-gradient(135deg, #ff7a7a 0%, #FA5151 100%);
        color: white;
        border: none;
        border-radius: 4px;
        padding: 4px 8px;
        cursor: pointer;
        font-size: 12px;
        min-width: 40px;
    }

    .api-key-status {
        font-size: 11px;
        padding: 2px 6px;
        border-radius: 3px;
        font-weight: 500;
    }

    .api-key-current {
        background: #e8f5e8;
        color: #07C160;
    }

    .api-key-inactive {
        background: #f5f5f5;
        color: #999;
    }

    .empty-api-keys {
        text-align: center;
        color: #a3b1c2;
        padding: 20px;
        font-size: 13px;
    }

    /* ========== 新增：API密钥弹窗样式 ========== */
    .api-keys-modal {
        display: none;
        position: fixed;
        z-index: 10003;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.5);
        backdrop-filter: blur(5px);
    }

    .api-keys-modal-content {
        background-color: #fefefe;
        margin: 10% auto;
        padding: 0;
        border: none;
        width: 600px;
        max-width: 90%;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        overflow: hidden;
    }

    .api-keys-modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .api-keys-modal-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }

    .modal-close-btn {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s;
    }

    .modal-close-btn:hover {
        background-color: rgba(255,255,255,0.2);
    }

    .api-keys-modal-body {
        padding: 25px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .api-keys-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .api-key-modal-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #f8f9ff;
        border-radius: 8px;
        border: 1px solid #e1e8f0;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .api-key-modal-item:hover {
        background: #f0f4f8;
        border-color: #d1d9e0;
    }

    .api-key-modal-item.disabled {
        background: #fff5f5;
        border-color: #fecaca;
    }

    .api-key-modal-display {
        flex: 1;
        font-family: monospace;
        color: #5a6c80;
        margin-right: 15px;
        font-size: 13px;
    }

    .api-key-modal-actions {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .api-key-modal-status {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 500;
        min-width: 60px;
        text-align: center;
    }

    .api-key-modal-status.current {
        background: #e8f5e8;
        color: #07C160;
    }

    .api-key-modal-status.available {
        background: #f0f9ff;
        color: #0ea5e9;
    }

    .api-key-modal-status.disabled {
        background: #fef2f2;
        color: #dc2626;
    }

    .btn-modal-delete-api {
        background: linear-gradient(135deg, #ff7a7a 0%, #FA5151 100%);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 6px 12px;
        cursor: pointer;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .btn-modal-delete-api:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(250, 81, 81, 0.3);
    }

    .btn-modal-validate-api {
        background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 6px 12px;
        cursor: pointer;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.2s ease;
        margin-right: 8px;
    }

    .btn-modal-validate-api:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
    }

    .btn-modal-validate-api:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .api-keys-status-display {
        padding: 10px;
        background: #f8f9ff;
        border-radius: 6px;
        border: 1px solid #e1e8f0;
        font-size: 13px;
        color: #5a6c80;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .api-keys-status-display:hover {
        background: #f0f4f8;
        border-color: #d1d9e0;
    }

    /* ========== 新增：深度思考功能样式 ========== */
    .deep-thinking-section {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid #f0f0f0;
    }

    .deep-thinking-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #5a6c80;
    }

    .deep-thinking-info {
        font-size: 11px;
        color: #888;
        margin-top: 4px;
    }

    /* 新增表单样式 */
    .form-row {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
    }

    .form-label {
        font-size: 13px;
        color: #5a6c80;
        min-width: 80px;
        font-weight: 500;
    }

    .form-input {
        flex: 1;
        padding: 8px;
        border-radius: 6px;
        border: 1px solid #e1e8f0;
        font-size: 13px;
    }



    /* ========== 新增：状态监控面板 ========== */
    .status-monitor-section {
        margin-top: 0;
        padding-top: 0;
        border-top: none;
    }
     .status-monitor-section:not(:last-child) {
        margin-bottom: 15px; /* 同组内元素间距 */
    }

    .status-monitor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .status-monitor-list {
        max-height: 120px;
        overflow-y: auto;
        border: 1px solid #eaeaea;
        border-radius: 8px;
        padding: 8px;
        background: #f8f9ff;
    }

    .status-monitor-item {
        padding: 8px;
        border-bottom: 1px solid #f0f0f0;
        font-size: 12px;
        display: flex;
        justify-content: space-between;
    }

    .status-monitor-item:last-child {
        border-bottom: none;
    }

    .status-monitor-time {
        color: #888;
        font-size: 11px;
    }

    .status-monitor-warning {
        color: #ff9800;
    }

    .status-monitor-error {
        color: #f44336;
    }

    .status-monitor-success {
        color: #4caf50;
    }

    .status-monitor-info {
        color: #2196f3;
    }

    /* ========== 新增：可折叠组合样式 ========== */
    .collapsible-group {
        border: 1px solid #e1e8f0;
        border-radius: 10px;
        overflow: hidden;
        background: #ffffff;
        transition: all 0.3s ease-out;
    }

    .group-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 15px;
        cursor: pointer;
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f4f8 100%);
        border-bottom: 1px solid #e1e8f0;
        transition: background-color 0.2s;
    }

    .group-header:hover {
        background: #e1e8f0;
    }

    .group-title {
        font-size: 15px;
        color: #2c3e50;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .group-toggle-icon {
        transition: transform 0.3s ease;
    }

    .group-content {
        padding: 15px;
        transition: all 0.3s ease-out;
        max-height: 2000px; /* 一个较大的值以允许内容完全显示 */
        opacity: 1;
    }

    .collapsible-group.collapsed {
         background: #f8f9ff;
    }

    .collapsible-group.collapsed .group-content {
        max-height: 0;
        opacity: 0;
        padding-top: 0;
        padding-bottom: 0;
        margin: 0;
        border: none;
        overflow: hidden;
    }

    .collapsible-group.collapsed .group-header {
        border-bottom: 1px solid transparent;
    }

    .collapsible-group.collapsed .group-toggle-icon {
        transform: rotate(-90deg);
    }

    /* ========== 新增：历史日志弹窗样式 ========== */
    .log-history-modal {
        display: none;
        position: fixed;
        z-index: 10002;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.5);
    }

    .log-history-modal-content {
        background-color: #fefefe;
        margin: 10% auto;
        padding: 20px;
        border: 1px solid #888;
        width: 80%;
        max-width: 800px;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        display: flex;
        flex-direction: column;
    }

    .log-history-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .log-history-modal-header h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }

    #closeLogHistoryModalBtn {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        background: none;
        border: none;
        cursor: pointer;
        line-height: 1;
    }

    #closeLogHistoryModalBtn:hover,
    #closeLogHistoryModalBtn:focus {
        color: black;
        text-decoration: none;
    }

    .log-history-modal-body {
        margin-top: 15px;
        max-height: 60vh;
        overflow-y: auto;
        padding-right: 10px;
    }









    /* ========== 新增：自动检查会话未回复的功能增强 ========== */
    /* 【功能优化】移除边框和内边距 */
    .auto-check-section {
        margin-top: 0;
        padding-top: 0;
        border-top: none;
    }
    .auto-check-section:not(:last-child) {
        margin-bottom: 15px; /* 同组内元素间距 */
    }

    .check-status {
        font-size: 12px;
        color: #5a6c80;
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .check-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #FA5151;
    }

    .check-indicator.active {
        background-color: #07C160;
    }



    /* ========== 新增：用户会话列表 ========== */
    .user-session-section {
        margin-top: 0;
        padding-top: 0;
        border-top: none;
    }
    .user-session-section:not(:last-child) {
        margin-bottom: 15px; /* 同组内元素间距 */
    }


    .user-session-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .user-session-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #eaeaea;
        border-radius: 8px;
        padding: 8px;
        background: #f8f9ff;
    }

    .user-session-item {
        padding: 8px 10px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
    }

    .user-session-item:last-child {
        border-bottom: none;
    }

    .user-session-info {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: flex; /* 使用flex布局 */
        justify-content: space-between; /* 两端对齐 */
        align-items: center; /* 垂直居中 */
    }

    .user-session-name {
        font-weight: 500;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 10px;
    }

    .user-session-status {
        font-size: 12px;
        color: #666;
        padding: 2px 6px;
        border-radius: 4px;
        white-space: nowrap;
    }

    .user-session-status.user-session-active {
        color: #E6A23C; /* 橙色，表示未回复或失败 */
        background-color: #fdf6ec;
    }
    .user-session-status.user-session-pending {
        color: #409EFF; /* 蓝色，表示扫描中/查询中 */
        background-color: #ecf5ff;
    }
    .user-session-status.user-session-completed {
        color: #67C23A; /* 绿色，表示已处理/已结束 */
        background-color: #f0f9eb;
    }


    /* 深度思考功能样式已删除 */



    .ai-training-stats {
        font-size: 12px;
        color: #5a6c80;
        text-align: center;
        margin-top: 5px;
    }

    /* ========== 新增：AI智能体知识库样式 ========== */
    .ai-agent-knowledge-section {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
    }

    .ai-training-dialog {
        /* 【修复】默认隐藏，但确保显示时有正确的布局 */
        display: none;
        position: fixed;
        z-index: 10001; /* 比主面板高一级 */
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);

        /* 【优化】设置A4纸张标准尺寸，与控制面板保持完全一致 */
        width: 1188px;
        height: 842px; /* 【优化】改为A4纸张标准高度，与控制面板原始设计一致 */
        max-width: 95vw;
        max-height: 95vh;

        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 16px; /* 与控制面板保持一致的圆角 */
        box-shadow: 0 12px 60px rgba(0, 0, 0, 0.25); /* 与控制面板保持一致的阴影 */

        /* 【修复】确保显示时使用flex布局 */
        flex-direction: column;
        resize: none; /* 禁用自由缩放 */
        overflow: hidden; /* 【重要】改为hidden，防止整体滚动影响布局 */
        min-width: 600px;
        min-height: 842px; /* 【优化】设置与height相同的A4标准最小高度 */
        backdrop-filter: blur(20px);

        /* 【新增】与控制面板一致的字体和过渡效果 */
        font-family: -apple-system, BlinkMacSystemFont, "Segue UI", Roboto, Helvetica, Arial, sans-serif;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1); /* 添加边框保持一致性 */
    }

    /* 【新增】显示状态时的样式 */
    .ai-training-dialog[style*="display: flex"] {
        display: flex !important;
    }

    .ai-training-dialog-header {
        padding: 20px 25px;
        cursor: move;
        z-index: 10;
        background: rgba(255, 255, 255, 0.15);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        border-top-left-radius: 16px; /* 【优化】与对话框圆角保持一致 */
        border-top-right-radius: 16px; /* 【优化】与对话框圆角保持一致 */
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
        font-weight: 700;
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);

        /* 【新增】确保头部不会因为内容过多而变形 */
        flex-shrink: 0;
        min-height: 60px;
    }

    .header-controls {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .header-btn {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        font-size: 18px;
        cursor: pointer;
        color: white;
        line-height: 1;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        font-family: Arial, sans-serif;
        font-weight: normal;
        padding: 0;
        margin: 0;
        text-align: center;
        vertical-align: middle;
    }

    .header-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    /* 搜索栏样式 */
    .search-bar {
        background: rgba(255, 255, 255, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: 15px 25px;
        backdrop-filter: blur(10px);
        animation: slideDown 0.3s ease;
    }

    .search-container {
        display: flex;
        align-items: center;
        gap: 10px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 25px;
        padding: 8px 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    #searchInput {
        flex: 1;
        border: none;
        background: transparent;
        outline: none;
        font-size: 14px;
        color: #333;
        padding: 5px 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    }

    #searchInput::placeholder {
        color: #999;
    }

    .search-controls {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .search-controls button {
        background: rgba(102, 126, 234, 0.1);
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 4px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
        color: rgba(102, 126, 234, 0.8);
        transition: all 0.2s ease;
    }

    .search-controls button:hover {
        background: rgba(102, 126, 234, 0.2);
        border-color: rgba(102, 126, 234, 0.4);
    }

    .search-controls button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    #searchResults {
        font-size: 12px;
        color: #666;
        min-width: 30px;
        text-align: center;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 搜索高亮样式 */
    .search-highlight {
        background: #ffeb3b !important;
        color: #333 !important;
        padding: 2px 4px;
        border-radius: 3px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    }

    .search-highlight.current {
        background: #ff9800 !important;
        color: white !important;
    }



    .ai-training-dialog-body {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden; /* 内部滚动 */
        height: calc(100% - 80px); /* 【关键修复】减去header高度，确保占满剩余空间 */
        min-height: 742px; /* 【优化】调整为A4高度减去header高度 (842-80=762px，留20px缓冲) */
    }

    .ai-training-messages {
        flex: 1; /* 【关键修复】改为flex: 1，确保占满可用空间 */
        overflow-y: auto;
        padding: 30px; /* 【优化】增加内边距，适应更大的宽度 */
        background: rgba(255, 255, 255, 0.95);
        position: relative; /* 【新增】为撤回弹窗提供定位上下文 */
        backdrop-filter: blur(20px);
        margin: 0;

        /* 【优化】设置合适的高度，适配A4纸张尺寸 */
        height: calc(100% - 120px); /* 减去输入区域高度 */
        min-height: 540px; /* 【优化】调整为适合A4高度的消息区域最小高度 */

        /* 【新增】优化滚动条样式 */
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    }

    /* 【新增】Webkit浏览器滚动条样式 */
    .ai-training-messages::-webkit-scrollbar {
        width: 8px;
    }

    .ai-training-messages::-webkit-scrollbar-track {
        background: transparent;
    }

    .ai-training-messages::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
    }

    .ai-training-messages::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
    }

    .ai-training-msg {
        display: flex;
        margin-bottom: 20px;
        max-width: 75%; /* 【优化】适应更宽的对话框，稍微减少最大宽度比例 */
        animation: fadeInUp 0.3s ease;
        align-items: flex-end; /* 让头像和气泡底部对齐 */
    }
    .ai-training-msg.user {
        margin-left: auto;
        flex-direction: row-reverse;
    }
    .ai-training-msg .avatar {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        text-align: center;
        line-height: 42px;
        font-weight: 600;
        flex-shrink: 0;
        color: white;
        font-size: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        margin-bottom: 5px; /* 让头像稍微上移一点，与气泡更好对齐 */
    }
    .ai-training-msg.user .avatar {
        background: linear-gradient(135deg, #95EC69 0%, #7ED321 100%); /* 【优化】改为微信绿色，与消息气泡保持一致 */
    }
    .ai-training-msg.bot .avatar {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    .ai-training-msg .content {
        margin: 0 15px;
        padding: 15px 20px;
        border-radius: 18px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        font-size: 14px;
        word-wrap: break-word;
        white-space: pre-wrap; /* 保持格式 */
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        position: relative;
        line-height: 1.5;
    }
    .ai-training-msg.user .content {
        background: linear-gradient(135deg, #95EC69 0%, #7ED321 100%); /* 【优化】改为微信绿色 */
        color: white;
        cursor: pointer; /* 【新增】用户消息可点击 */
    }
    /* 用户消息气泡箭头 */
    .ai-training-msg.user .content::after {
        content: '';
        position: absolute;
        right: -8px;
        bottom: 15px;
        width: 0;
        height: 0;
        border-left: 8px solid #7ED321; /* 【优化】箭头颜色也改为绿色 */
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
    }
    .ai-training-msg.user .content:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }
    .ai-training-msg.bot .content {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid rgba(0,0,0,0.05);
    }
    /* AI回复气泡箭头 */
    .ai-training-msg.bot .content::before {
        content: '';
        position: absolute;
        left: -8px;
        bottom: 15px;
        width: 0;
        height: 0;
        border-right: 8px solid #ffffff;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 对话框显示动画 */
    .ai-training-dialog[style*="flex"] {
        animation: dialogFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes dialogFadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
    }

    /* 滚动条美化 */
    .ai-training-messages::-webkit-scrollbar {
        width: 6px;
    }
    .ai-training-messages::-webkit-scrollbar-track {
        background: rgba(0,0,0,0.1);
        border-radius: 3px;
    }
    .ai-training-messages::-webkit-scrollbar-thumb {
        background: rgba(102, 126, 234, 0.3);
        border-radius: 3px;
    }
    .ai-training-messages::-webkit-scrollbar-thumb:hover {
        background: rgba(102, 126, 234, 0.5);
    }

    /* 【新增】AI训练消息撤回弹窗 */
    .retract-popup {
        position: absolute;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 13px;
        color: #d9534f;
        cursor: pointer;
        z-index: 10002;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        user-select: none;
        transition: background-color 0.2s;
    }
    .retract-popup:hover {
        background-color: #f5f5f5;
    }

    .ai-training-input-area {
        padding: 30px; /* 【优化】增加内边距，适应更大的宽度 */
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        position: relative;
        flex-shrink: 0; /* 【优化】确保输入区域不会被压缩 */
        min-height: 120px; /* 【优化】改为最小高度，允许自适应 */
        box-sizing: border-box; /* 【重要】确保padding包含在高度内 */
    }
    /* 【新增】问答训练按钮区域 */
    .qa-training-section {
        display: flex;
        justify-content: center;
        margin-bottom: 15px;
    }

    .qa-training-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .qa-training-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .qa-training-btn:active {
        transform: translateY(0);
    }

    .ai-training-input-container {
        position: relative;
        display: flex;
        align-items: center; /* 改为center对齐 */
        gap: 8px;
    }
    .upload-file-btn {
        width: 40px; /* 稍微增大以更好对齐 */
        height: 40px;
        border-radius: 50%;
        border: 2px solid rgba(102, 126, 234, 0.3);
        background: rgba(255, 255, 255, 0.9);
        color: rgba(102, 126, 234, 0.8);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        font-size: 18px;
        font-weight: bold;
        backdrop-filter: blur(10px);
        flex-shrink: 0;
        margin-bottom: 5px; /* 微调垂直位置 */
    }
    .upload-file-btn:hover {
        background: rgba(102, 126, 234, 0.1);
        border-color: rgba(102, 126, 234, 0.6);
        transform: scale(1.05);
    }
    .ai-training-input-area textarea {
        flex-grow: 1;
        padding: 15px 60px 15px 20px;
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 25px;
        resize: none;
        font-size: 14px;
        min-height: 50px; /* 【优化】改为最小高度 */
        height: auto; /* 【优化】允许自动调整高度 */
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        outline: none;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        line-height: 1.4;
        overflow-y: hidden; /* 【优化】隐藏垂直滚动条，使用自动高度 */

        /* 【优化】适应更宽的对话框 */
        min-width: 0; /* 确保在flex容器中可以正确收缩 */
        max-height: 144px; /* 【优化】6行的最大高度 (24px * 6) */
    }
    .ai-training-input-area textarea:focus {
        border-color: rgba(102, 126, 234, 0.6);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: rgba(255, 255, 255, 1);
    }
    .ai-training-input-area textarea::placeholder {
        color: rgba(0, 0, 0, 0.4);
    }
    #sendTrainingMsgBtn {
        width: 40px; /* 与上传按钮保持一致 */
        height: 40px;
        border-radius: 50%;
        border: none;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        font-size: 16px;
        flex-shrink: 0;
        margin-bottom: 5px; /* 与上传按钮保持一致的垂直位置 */
    }
    #sendTrainingMsgBtn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
    #sendTrainingMsgBtn:active {
        transform: scale(0.95);
    }

    /* 文件预览区域样式 */
    .uploaded-files-preview {
        margin-top: 15px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        max-height: 120px;
        overflow-y: auto;
    }
    .file-preview-item {
        display: flex;
        align-items: center;
        background: rgba(102, 126, 234, 0.1);
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 12px;
        color: rgba(102, 126, 234, 0.8);
        position: relative;
        max-width: 200px;
    }
    .file-preview-item .file-icon {
        margin-right: 6px;
        font-size: 14px;
    }
    .file-preview-item .file-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .file-preview-item .file-remove {
        margin-left: 6px;
        cursor: pointer;
        color: rgba(255, 0, 0, 0.6);
        font-weight: bold;
        font-size: 14px;
        line-height: 1;
    }
    .file-preview-item .file-remove:hover {
        color: rgba(255, 0, 0, 0.8);
    }
    .file-processing {
        opacity: 0.6;
        pointer-events: none;
    }
    .file-processing::after {
        content: "处理中...";
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 10px;
        color: rgba(102, 126, 234, 0.6);
    }




    /* ========== 【新增】店铺商品设置样式 ========== */
    .shop-products-section {
        margin-top: 0;
        padding-top: 0;
        border-top: none;
    }

    .shop-products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 15px;
        padding: 10px;
    }

    .shop-product-card {
        background: white;
        border: 1px solid #e1e8f0;
        border-radius: 8px;
        padding: 10px;
        transition: all 0.2s;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        display: flex;
        flex-direction: column;
    }

    .shop-product-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .shop-product-image {
        width: 100%;
        height: 120px;
        object-fit: cover;
        border-radius: 6px;
        margin-bottom: 8px;
    }

    .shop-product-title {
        font-size: 13px;
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        flex-grow: 1;
    }

    .shop-product-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
    }

    .shop-product-id {
        color: #666;
        background: #f0f4f8;
        padding: 2px 6px;
        border-radius: 4px;
    }

    .shop-product-price {
        color: #ff6b6b;
        font-weight: 600;
    }

    .shop-product-description {
        width: 100%;
        padding: 6px;
        border: 1px solid #e1e8f0;
        border-radius: 4px;
        font-size: 12px;
        resize: vertical;
        min-height: 60px;
        background: #f9f9f9;
        margin-top: auto;
    }

    .shop-product-description:focus {
        outline: none;
        border-color: #10AEFF;
        background: white;
    }

    /* 【新增】商品快捷操作按钮样式 */
    .shop-product-actions {
        display: flex;
        gap: 8px;
        margin-top: 8px;
    }

    .shop-product-btn {
        flex: 1;
        padding: 6px 8px;
        border-radius: 6px;
        border: 1px solid #ddd;
        font-size: 12px;
        cursor: pointer;
        background-color: #f7f7f7;
        transition: all 0.2s;
        white-space: nowrap;
    }

    .shop-product-btn:hover {
        background-color: #e9e9e9;
        border-color: #ccc;
    }

    .btn-invite {
        color: #07c160;
        border-color: #a8e6c2;
    }
    .btn-invite:hover {
        background-color: #e6f8ee;
    }

    .btn-send {
        color: #10aeff;
        border-color: #a5dfff;
    }
    .btn-send:hover {
        background-color: #e7f7ff;
    }


    .shop-products-loading {
        text-align: center;
        padding: 40px;
        color: #666;
        font-size: 14px;
    }

    .shop-products-empty {
        text-align: center;
        padding: 40px;
        color: #999;
        font-size: 14px;
    }

    .shop-products-stats {
        font-size: 13px;
        color: #5a6c80;
        text-align: center;
        padding: 8px;
        background: #f0f4f8;
        border-radius: 6px;
    }

    /* ========== 【新增】商品提取结果折叠与弹窗样式 ========== */
    .shop-products-view-trigger {
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f4f8 100%);
        border: 1px solid #e1e8f0;
        border-radius: 8px;
        padding: 15px 20px;
        margin-top: 15px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.25s ease;
    }
    .shop-products-view-trigger:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        border-color: #d1d9e6;
    }
    .trigger-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }
    .trigger-label {
        font-size: 14px;
        color: #5a6c80;
        font-weight: 500;
    }
    .trigger-count {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
    }
    .trigger-action {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #667eea;
        font-weight: 600;
    }
    /* 商品弹窗样式 */
    .shop-products-modal {
        display: none; /* 默认隐藏 */
        position: fixed;
        z-index: 10002; /* 比训练弹窗高一级 */
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background-color: rgba(0,0,0,0.6);
        backdrop-filter: blur(5px);
        animation: fadeIn 0.3s ease;
    }
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    .shop-products-modal-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: #fdfdff;
        border-radius: 16px;
        box-shadow: 0 12px 60px rgba(0, 0, 0, 0.25);
        width: 90%;
        max-width: 1200px;
        height: 90vh;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        animation: scaleUp 0.3s ease;
    }
    @keyframes scaleUp {
        from { transform: translate(-50%, -50%) scale(0.95); opacity: 0; }
        to { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    }
    .shop-products-modal-header {
        padding: 15px 25px;
        border-bottom: 1px solid #e0e6f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
    }
    .shop-products-modal-header h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }
    #closeShopProductsModalBtn {
        background: none;
        border: none;
        font-size: 28px;
        font-weight: bold;
        color: #aaa;
        cursor: pointer;
        line-height: 1;
        transition: color 0.2s;
    }
    #closeShopProductsModalBtn:hover {
        color: #333;
    }
    .shop-products-modal-body {
        flex-grow: 1;
        overflow-y: auto;
        padding: 20px;
        background-color: #f4f7fa;
    }
    .shop-products-modal-footer {
        padding: 10px 25px;
        border-top: 1px solid #e0e6f0;
        background-color: #fff;
        flex-shrink: 0;
    }
    `);

    // ========== 安全日志系统 ==========
    // 创建一个安全的日志系统，防止代码信息泄露到开发者工具
    const SecureLogger = {
        // 是否启用调试模式（生产环境应设为false）
        DEBUG_MODE: false,

        // 内部日志存储（仅用于脚本内部状态监控）
        internalLogs: [],
        maxInternalLogs: 100,

        // 安全的日志方法，不会输出到console
        log: function(message, level = 'info') {
            if (!this.DEBUG_MODE) return; // 生产模式下完全禁用

            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp,
                level,
                message: typeof message === 'string' ? message : '[Object]'
            };

            this.internalLogs.push(logEntry);
            if (this.internalLogs.length > this.maxInternalLogs) {
                this.internalLogs.shift();
            }
        },

        info: function(message) { this.log(message, 'info'); },
        warn: function(message) { this.log(message, 'warn'); },
        error: function(message) { this.log(message, 'error'); },

        // 获取内部日志（仅供脚本内部使用）
        getInternalLogs: function() {
            return this.internalLogs.slice();
        },

        // 清空内部日志
        clearInternalLogs: function() {
            this.internalLogs = [];
        }
    };

    // 当前状态
    let isAutoReplyEnabled = false;
    let todayReplies = 0;
    let lastReplyTime = '--:--:--';
    // 【功能修复】增加一个全局锁，用于在执行跨页面等任务时，暂停其他操作
    let processingSession = false;
    let processingSessionTimeout = null; // 【功能修复】新增用于订单查询的超时定时器
    // 【功能修复】将 processedMessages 作为内存缓存，解决重复消息不回复问题
    // 不再从 GM_storage 加载或保存，确保每次页面加载都是全新的
    let processedMessages = new Map(); // sessionId -> Set of messageIds
    let currentSessionId = null;
    let sessionPollingTimer = null;
    let messageCheckTimer = null;
    let lastMessageCount = 0;
    let lastMessageSnapshot = '';
    let lastSentMessageContent = '';

    // 新增状态
    let customerServiceName = ''; // 客服名字
    let skipSystemReplies = true; // 新增：跳过系统回复标志, 默认开启

    // ========== 恢复：AI对话功能状态 ==========
    let aiEnabled = true; // 【优化1】是否启用AI回复 - 默认开启
    let aiApiKeys = []; // 多个DeepSeek API密钥数组
    let aiApiKeyStatus = []; // API密钥状态数组，true=可用，false=不可用
    let currentApiKeyIndex = 0; // 当前使用的API密钥索引
    let aiModel = 'deepseek-chat'; // 默认模型
    let deepThinkingEnabled = false; // 新增：深度思考(R1)功能开关
    let aiSystemPrompt = '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。';
    let aiStatus = 'inactive'; // AI状态：inactive/active/error
    let aiThinking = false; // 新增：AI思考状态标志
    let aiReplyDelay = 0; // 新增：AI回复延迟时间（秒）
    let conversationContexts = new Map(); // 新增：会话上下文，key为sessionId，value为消息数组
    let aiReplyTimers = new Map(); // 新增：AI回复定时器，key为messageId，value为定时器ID

    // ========== 【优化2】新增：内置API密钥配置区域 ==========
    // 【重要说明】请在下方的字符串中输入您的DeepSeek API密钥
    // 格式：一行一个密钥，以sk-开头
    // 示例：
    // sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    // sk-yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy
    const BUILTIN_API_KEYS = `
请在这里输入您的API密钥，一行一个：
sk-b9ff9d44021f445eb74047b2ae2ad9ab
    `.trim();

    // ========== 新增：AI会话监控状态 ==========
    let aiSessionMonitors = new Map(); // sessionId -> {lastAITime, lastUserMessageTime, pendingReply}

    // 适配后保留的会话监控，主要为"自动结束对话"功能提供支持
    let monitoredSessions = new Map(); // sessionId -> {element, name, status, lastUserReplyTime}

    // ========== [功能修复] 自动结束对话相关状态 (全新逻辑) ==========
    let autoEndInterval = 0; // 自动结束等待时间（秒）
    let autoEndNextTriggerTimestamp = 0; // 下一次触发自动结束扫描的时间戳
    let autoEndCountdownIntervalId = null; // 用于显示倒计时的定时器ID
    let isAutoEnding = false; // 标志位，防止自动结束功能与主扫描功能冲突

    // ========== AI功能已删除 ==========
    let aiScanTimer = null; // AI会话扫描定时器
    let lastMessageTimes = new Map(); // 新增：会话最后消息时间

    // ========== 新增：状态监控状态 ==========
    let statusMonitorEvents = []; // 状态监控事件 (UI显示用)
    let historicalLogs = []; // 历史日志记录 (持久化存储用)
    let lastUnrepliedUserMessage = null; // 记录最后未回复的用户消息
    let sessionReplyStatus = new Map(); // 新增：会话回复状态追踪
    // AI思考状态已删除



    // ========== 【优化2】新增：内置API密钥解析函数 ==========
    /**
     * 解析内置API密钥配置
     * @returns {string[]} 有效的API密钥数组
     */
    const parseBuiltinApiKeys = withErrorHandling(function() {
        if (!BUILTIN_API_KEYS || typeof BUILTIN_API_KEYS !== 'string') {
            return [];
        }

        const lines = BUILTIN_API_KEYS.split('\n')
            .map(line => line.trim())
            .filter(line => line && !line.startsWith('请在这里') && !line.startsWith('sk-your-api-key-here'));

        const validKeys = [];
        for (const line of lines) {
            if (line.startsWith('sk-') && line.length > 20) {
                validKeys.push(line);
            }
        }

        if (validKeys.length > 0) {
            addStatusMonitorEvent(`发现 ${validKeys.length} 个内置API密钥`, 'info');
        }

        return validKeys;
    }, '解析内置API密钥');

    // ========== 新增：AI智能体知识库状态 ==========
    let aiAgentKnowledgeBase = {
        conversations: [], // { question: '...', answer: '...' }
        dynamic_rules: [] // { trigger_keywords: [], selector: '...', template: '...' }
    };

    // ========== 新增：文件上传和处理状态 ==========
    let uploadedFiles = []; // 存储上传的文件信息
    let fileProcessingQueue = []; // 文件处理队列

    // ========== 新增：搜索功能状态 ==========
    let searchResults = []; // 搜索结果数组
    let currentSearchIndex = -1; // 当前搜索结果索引
    let searchQuery = ''; // 当前搜索关键词
    let trainingDialogMessages = []; // 用于存储AI训练对话框中的对话记录
    let isTrainingDialogDragging = false;
    let dragOffsetX, dragOffsetY;

    // ========== 新增：实时监控未读消息状态 ==========
    let unreadMonitorTimer = null; // 未读消息监控定时器
    let isProcessingUnread = false; // 是否正在处理未读消息
    let isAutoChecking = false; // 是否正在自动检查（保留用于其他功能的互斥控制）
    let autoCheckTimer = null; // 自动检查定时器
    let unreadQueue = []; // 未读消息队列

    // ========== 【优化】控制面板折叠状态 ==========
    let groupStates = {}; // 用于存储每个组合的折叠状态

    // ========== 【新增】客服自动登记反馈状态 ==========
    let feedbackRegistry = new Map(); // key: userNickname, value: { ...feedback data }
    let isReplyingToFeedback = false; // 批量回复的互斥锁

    // ========== 【新增】店铺商品设置状态 ==========
    let shopProducts = []; // 店铺商品列表
    let shopProductDescriptions = new Map(); // 商品ID -> 描述文本

    // ========== 【新增】会话转接功能状态 ==========
    let transferKeywords = '';
    let transferToAgent = '';
    let availableAgents = [];

    // ===================================================================================
    // 【核心重构 V9.0.3】订单查询流程优化
    // ===================================================================================

    // --- 跨页面任务管理核心 ---
    const TASK_STORAGE_KEY_PREFIX = 'autoQueryTask_v9_';

    /**
     * @description [工具函数] 带重试的元素等待函数，确保在动态加载的页面中能稳定找到目标。
     * @param {string} selector - CSS选择器。
     * @param {object} [options={}] - 额外选项，如 { textContent, searchContext, timeout }。
     * @returns {Promise<Element>} 查找到的元素。
     */
    function waitForElement(selector, options = {}) {
        const { textContent = null, searchContext = document, timeout = 10000 } = options; // 默认超时10秒
        return new Promise((resolve, reject) => {
            const intervalTime = 300; // 检查频率
            const endTime = Date.now() + timeout;

            const timer = setInterval(() => {
                const context = searchContext.shadowRoot ? searchContext.shadowRoot : searchContext;
                const elements = Array.from(context.querySelectorAll(selector));
                let foundElement = null;

                if (textContent) {
                    // 如果需要匹配文本，则进行更灵活的查找
                    foundElement = elements.find(el => el.textContent && el.textContent.trim().includes(textContent));
                } else {
                    // 否则，只查找存在的元素
                    foundElement = elements.length > 0 ? elements[0] : null;
                }

                // 确保元素不仅存在，而且是可见的 (offsetParent !== null)
                if (foundElement && foundElement.offsetParent !== null) {
                    clearInterval(timer);
                    resolve(foundElement);
                } else if (Date.now() > endTime) {
                    clearInterval(timer);
                    reject(new Error(`查找元素超时 (timeout: ${timeout}ms): ${selector} ${textContent ? `with text "${textContent}"` : ''}`));
                }
            }, intervalTime);
        });
    }

    /**
     * @description [工具函数] 快速元素等待函数，专为转接功能优化，减少等待时间。
     * @param {string} selector - CSS选择器。
     * @param {object} [options={}] - 额外选项，如 { textContent, searchContext, timeout }。
     * @returns {Promise<Element>} 查找到的元素。
     */
    function waitForElementFast(selector, options = {}) {
        const { textContent = null, searchContext = document, timeout = 3000 } = options; // 默认超时3秒
        return new Promise((resolve, reject) => {
            const intervalTime = 50; // 快速检查频率：50ms
            const endTime = Date.now() + timeout;

            const timer = setInterval(() => {
                const context = searchContext.shadowRoot ? searchContext.shadowRoot : searchContext;
                const elements = Array.from(context.querySelectorAll(selector));
                let foundElement = null;

                if (textContent) {
                    // 如果需要匹配文本，则进行更灵活的查找
                    foundElement = elements.find(el => el.textContent && el.textContent.trim().includes(textContent));
                } else {
                    // 否则，只查找存在的元素
                    foundElement = elements.length > 0 ? elements[0] : null;
                }

                // 确保元素不仅存在，而且是可见的 (offsetParent !== null)
                if (foundElement && foundElement.offsetParent !== null) {
                    clearInterval(timer);
                    resolve(foundElement);
                } else if (Date.now() > endTime) {
                    clearInterval(timer);
                    reject(new Error(`快速查找元素超时 (timeout: ${timeout}ms): ${selector} ${textContent ? `with text "${textContent}"` : ''}`));
                }
            }, intervalTime);
        });
    }

    /**
     * @description [查询页面任务运行器] 在新打开的页面加载时运行，检查并执行自动化查询任务。
     */
    const runAutoQueryTaskInNewTab = withErrorHandling(async function() {
        // 这个函数只应该在被打开的查询页面运行
        const urlParams = new URLSearchParams(window.location.search);
        const taskId = urlParams.get('autoQueryTaskId');
        if (!taskId) return;

        const taskKey = TASK_STORAGE_KEY_PREFIX + taskId;
        const taskJSON = localStorage.getItem(taskKey);
        if (!taskJSON) return;

        const task = JSON.parse(taskJSON);
        if (task.status !== 'pending') return;

        try {
            // 标记任务正在执行，防止重复操作
            task.status = 'executing';
            localStorage.setItem(taskKey, JSON.stringify(task));

            // 直接开始查询，因为已经是在订单列表页面
            const microApp = await waitForElement('micro-app[name="order"]');
            const shadowRoot = microApp.shadowRoot;
            if (!shadowRoot) throw new Error('无法访问订单管理微应用的 Shadow DOM！');

            // 聚焦并输入手机号后四位
            const phoneInput = await waitForElement('input[placeholder="填写收件人手机号后四位"]', { searchContext: shadowRoot });
            phoneInput.value = task.phoneSuffix;
            phoneInput.dispatchEvent(new Event('input', { bubbles: true, composed: true }));

            await new Promise(r => setTimeout(r, 100)); // 等待UI反应

            // 点击查询按钮
            const queryButton = await waitForElement('button', { textContent: '查询', searchContext: shadowRoot });
            queryButton.click();
            await new Promise(r => setTimeout(r, 2000)); // 等待查询结果加载

            // 提取结果
            const noDataElement = shadowRoot.querySelector('.weui-desktop-empty, .el-table__empty-text');
            if (noDataElement && noDataElement.offsetParent !== null) {
                task.result = `亲，根据您提供的手机尾号【${task.phoneSuffix}】没有查询到相关订单哦，麻烦您核对一下号码是否正确呢？如果号码无误，可能是还没有下单哦。`;
                task.success = false;
            } else {
                const orderElements = shadowRoot.querySelectorAll('.order, .order-item-wrap, .el-table__row');
                if (orderElements.length === 0) {
                     task.result = `亲，根据您提供的手机尾号【${task.phoneSuffix}】没有查询到相关订单哦，麻烦您核对一下号码是否正确呢？如果号码无误，可能是还没有下单哦。`;
                     task.success = false;
                } else {
                    const orderData = Array.from(orderElements).map(orderContainer => {
                        const orderIdSpan = Array.from(orderContainer.querySelectorAll('span.mr16, [class*="order-no"], [class*="order_id"]')).find(s => s.textContent.includes('订单号'));
                        const orderTimeSpan = Array.from(orderContainer.querySelectorAll('span.mr16, [class*="create-time"], [class*="order-time"]')).find(s => s.textContent.includes('下单时间'));
                        let orderStatusEl = orderContainer.querySelector('a[href="javascript:void(0);"], .order-body__status, div.font-\\[500\\], [class*="trade-status"]');

                        const orderId = orderIdSpan ? orderIdSpan.textContent.replace(/订单号|:|：/g, '').trim() : null;
                        const orderTime = orderTimeSpan ? orderTimeSpan.textContent.replace(/下单时间|:|：/g, '').trim() : '未知';
                        const orderStatus = orderStatusEl ? orderStatusEl.textContent.trim() : '未知';
                        return orderId ? { id: orderId, time: orderTime, status: orderStatus } : null;
                    }).filter(Boolean);

                    if (orderData.length > 0) {
                        let reply = `亲，为您查询到手机尾号为【${task.phoneSuffix}】的订单信息如下（最多显示3条）：\n\n`;
                        orderData.slice(0, 3).forEach((order, i) => {
                            reply += `[订单 ${i + 1}]：\n  - 订单号: ${order.id}\n  - 下单时间: ${order.time}\n  - 订单状态: ${order.status}\n\n`;
                        });
                         if (orderData.length > 3) {
                            reply += `共为您找到 ${orderData.length} 条相关订单。\n`;
                        }
                        task.result = reply.trim();
                        task.success = true;
                    } else {
                        task.result = `亲，根据您提供的手机尾号【${task.phoneSuffix}】查询到了订单，但系统在读取订单详情时遇到了小麻烦。您可以提供订单截图给我吗？我来帮您查看。`;
                        task.success = false;
                    }
                }
            }

            // 完成任务，将结果写回localStorage，这将触发主页面的 'storage' 事件
            task.status = 'completed';
            localStorage.setItem(taskKey, JSON.stringify(task));

        } catch (error) {
            const taskToFail = JSON.parse(localStorage.getItem(taskKey) || '{}');
            taskToFail.status = 'failed';
            taskToFail.result = "抱歉，系统在为您自动查询订单时遇到了一点小问题，请稍后再试或提供订单截图，我来为您手动查询。";
            taskToFail.success = false;
            localStorage.setItem(taskKey, JSON.stringify(taskToFail));
        } finally {
            // 无论成功失败，都尝试关闭标签页
            setTimeout(window.close, 1500);
        }
    }, '新标签页自动查询任务运行器');

     /**
     * @description [订单查询-任务发起函数] 在客服页面调用此函数，启动新标签页查询流程。
     * @param {string} phoneSuffix - 手机号后四位。
     * @param {string} sessionId - 发起查询的会话ID
     * @param {string} sessionName - 发起查询的会话名称
     */
    const queryOrdersByPhoneInNewTab = withErrorHandling(async function(phoneSuffix, sessionId, sessionName) {
        addStatusMonitorEvent(`启动新标签页订单查询，手机尾号: ${phoneSuffix}`, 'info');

        // 立即更新当前会话状态为"查询中"
        updateUserSessionList(sessionId, sessionName, 'pending');

        const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const taskKey = TASK_STORAGE_KEY_PREFIX + taskId;

        const task = {
            id: taskId,
            sessionId: sessionId,
            sessionName: sessionName,
            phoneSuffix: phoneSuffix,
            status: 'pending',
            result: null,
            success: false,
            timestamp: Date.now()
        };
        localStorage.setItem(taskKey, JSON.stringify(task));
        addStatusMonitorEvent('跨页面查询任务已创建，准备在后台打开新标签页...', 'info');

        const queryUrl = `https://store.weixin.qq.com/shop/order/list?autoQueryTaskId=${taskId}`;
        GM_openInTab(queryUrl, { active: false });

        // 确保客服页面始终处于浏览器主页面显示
        window.focus();

    }, '新标签页订单查询任务发起');

    /**
     * @description 监听localStorage变化，接收来自查询页面的结果
     */
    const handleStorageChange = withErrorHandling(function(e) {
        if (!e.key) return;

        // --- 订单查询结果处理 ---
        if (e.key.startsWith(TASK_STORAGE_KEY_PREFIX)) {
            const taskKey = e.key;
            const taskJSON = e.newValue;
            if (!taskJSON) return;

            const task = JSON.parse(taskJSON);
            // 只处理已完成或失败的任务
            if (task.status === 'completed' || task.status === 'failed') {
                // 清理任务，避免重复处理
                localStorage.removeItem(taskKey);

                // 【功能修复】无论成功失败，都清除超时定时器
                if (processingSessionTimeout) {
                    clearTimeout(processingSessionTimeout);
                    processingSessionTimeout = null;
                }

                // 【功能修复】收到结果后，释放会话处理锁，并更新会话状态
                processingSession = false;
                updateUserSessionList(task.sessionId, task.sessionName, 'completed');

                if (task.result) {
                    addStatusMonitorEvent(`[实时传输] 收到查询结果，准备回复用户: ${task.sessionName}`, 'success');
                    // 使用从任务中传回的会话ID进行回复
                    setTimeout(async () => {
                        await sendQuickReply(task.result, task.sessionId, `task_result_${task.id}`);
                    }, 1000); // 延迟1秒发送，确保UI稳定
                }
            }
            return; // 处理完订单查询后直接返回
        }

    }, '处理本地存储变化');


    // 错误处理装饰器
    function withErrorHandling(fn, context = '') {
        return function(...args) {
            try {
                const result = fn.apply(this, args);
                // 确保异步函数也能被捕获
                if (result && typeof result.catch === 'function') {
                    return result.catch(error => {
                        SecureLogger.error(`Async Error in ${context}: ${error.message}`);
                        addStatusMonitorEvent(`[错误] ${context}: 操作失败 - ${error.message}`, 'error');
                        if (context.includes('订单查询处理')) {
                             // 【功能修复】订单查询出错时，也要确保解锁
                            processingSession = false;
                            if (processingSessionTimeout) {
                                clearTimeout(processingSessionTimeout);
                                processingSessionTimeout = null;
                            }
                        } else if (context.includes('处理') || context.includes('回复')) {
                            // [功能修复] 错误发生时，重置所有状态标志，避免卡死
                            processingSession = false;
                            isAutoChecking = false;
                            isAutoEnding = false;
                            isReplyingToFeedback = false; // 新增互斥锁重置
                            aiThinking = false; // 恢复：AI思考状态重置
                            SecureLogger.warn(`Error in ${context}, reset all operational flags.`);
                        }
                    });
                }
                return result;
            } catch (error) {
                SecureLogger.error(`Error in ${context}: ${error.message}`);
                addStatusMonitorEvent(`[错误] ${context}: 操作失败 - ${error.message}`, 'error');
                 if (context.includes('订单查询处理')) {
                    // 【功能修复】订单查询出错时，也要确保解锁
                    processingSession = false;
                    if (processingSessionTimeout) {
                        clearTimeout(processingSessionTimeout);
                        processingSessionTimeout = null;
                    }
                } else if (context.includes('处理') || context.includes('回复')) {
                    // [功能修复] 错误发生时，重置所有状态标志，避免卡死
                    processingSession = false;
                    isAutoChecking = false;
                    isAutoEnding = false;
                    isReplyingToFeedback = false; // 新增互斥锁重置
                    aiThinking = false; // 恢复：AI思考状态重置
                    SecureLogger.warn(`Error in ${context}, reset all operational flags.`);
                }
            }
        };
    }


    // 加载保存的数据
    const loadSavedData = withErrorHandling(function() {
        try {
            // 【功能优化】使用 GM_getValue 的第三个参数设置默认值
            // 如果是首次运行（即GM_getValue找不到值），则使用默认值
            isAutoReplyEnabled = GM_getValue('isAutoReplyEnabled_v9', true); // 使用新键名并默认为true

            const savedReplies = GM_getValue('todayReplies', 0);
            const savedTime = GM_getValue('lastReplyTime', '--:--:--');
            const savedDay = GM_getValue('lastSaveDay', '');
            skipSystemReplies = true; // 默认开启，不再提供设置
            customerServiceName = GM_getValue('customerServiceName', '');
            autoEndInterval = GM_getValue('autoEndInterval', 0);
            autoEndNextTriggerTimestamp = GM_getValue('autoEndNextTriggerTimestamp', 0); // [功能修复] 加载下次触发时间

            // ========== 恢复：加载AI设置 ==========
            aiEnabled = GM_getValue('aiEnabled_v9', true); // 【优化1】默认开启AI回复

            // 【优化2】处理内置API密钥
            const builtinKeys = parseBuiltinApiKeys();

            // 加载多个API密钥，兼容旧版本单个密钥
            const savedApiKeys = GM_getValue('aiApiKeys', '[]');
            const savedApiKeyStatus = GM_getValue('aiApiKeyStatus', '[]');
            const oldApiKey = GM_getValue('aiApiKey', '');
            try {
                aiApiKeys = JSON.parse(savedApiKeys);
                aiApiKeyStatus = JSON.parse(savedApiKeyStatus);

                // 【优化2】合并内置API密钥到现有密钥列表
                if (builtinKeys.length > 0) {
                    for (const builtinKey of builtinKeys) {
                        if (!aiApiKeys.includes(builtinKey)) {
                            aiApiKeys.push(builtinKey);
                            aiApiKeyStatus.push(true); // 内置密钥默认可用
                            addStatusMonitorEvent(`已自动添加内置API密钥: ${builtinKey.substring(0, 8)}...`, 'success');
                        }
                    }
                }

                // 如果是空数组且有旧的单个密钥，则迁移
                if (aiApiKeys.length === 0 && oldApiKey) {
                    aiApiKeys = [oldApiKey];
                    aiApiKeyStatus = [true]; // 默认可用
                    GM_setValue('aiApiKeys', JSON.stringify(aiApiKeys));
                    GM_setValue('aiApiKeyStatus', JSON.stringify(aiApiKeyStatus));
                    GM_deleteValue('aiApiKey'); // 删除旧的单个密钥
                }
                // 确保状态数组长度与密钥数组一致
                while (aiApiKeyStatus.length < aiApiKeys.length) {
                    aiApiKeyStatus.push(true); // 新密钥默认可用
                }
                while (aiApiKeyStatus.length > aiApiKeys.length) {
                    aiApiKeyStatus.pop(); // 移除多余状态
                }

                // 【优化2】如果有内置密钥被添加，保存到存储
                if (builtinKeys.length > 0) {
                    GM_setValue('aiApiKeys', JSON.stringify(aiApiKeys));
                    GM_setValue('aiApiKeyStatus', JSON.stringify(aiApiKeyStatus));
                }
            } catch (e) {
                aiApiKeys = oldApiKey ? [oldApiKey] : [];
                aiApiKeyStatus = oldApiKey ? [true] : [];

                // 【优化2】即使出错也要添加内置密钥
                if (builtinKeys.length > 0) {
                    aiApiKeys = [...aiApiKeys, ...builtinKeys];
                    aiApiKeyStatus = [...aiApiKeyStatus, ...builtinKeys.map(() => true)];
                }
            }
            currentApiKeyIndex = GM_getValue('currentApiKeyIndex', 0);
            if (currentApiKeyIndex >= aiApiKeys.length) {
                currentApiKeyIndex = 0;
            }
            aiModel = GM_getValue('aiModel', 'deepseek-chat');
            deepThinkingEnabled = GM_getValue('deepThinkingEnabled', false); // 新增：深度思考状态
            aiReplyDelay = GM_getValue('aiReplyDelay', 0);

            // 【新增】加载会话转接设置
            transferKeywords = GM_getValue('transferKeywords', '');
            transferToAgent = GM_getValue('transferToAgent', '');
            try {
                availableAgents = JSON.parse(GM_getValue('availableAgents', '[]'));
            } catch (e) {
                availableAgents = [];
                SecureLogger.warn('加载客服列表缓存失败，将使用空列表');
            }

            // 【优化】加载折叠组状态，默认为空对象
            groupStates = GM_getValue('groupStates', {
                'group3': false, // 默认展开AI与知识库
                'group4': false, // 默认展开会话与监控
                'group5': false, // 默认展开客服反馈
                'group6': false  // 【新增】默认展开店铺商品设置
            });

            // 新增：加载AI智能体知识库
            aiAgentKnowledgeBase = GM_getValue('aiAgentKnowledgeBase', {
                conversations: [],
                dynamic_rules: []
            });
            trainingDialogMessages = GM_getValue('trainingDialogMessages', []);

            // 【新增】加载客服登记反馈数据
            const savedFeedback = GM_getValue('feedbackRegistry', '[]');
            try {
                feedbackRegistry = new Map(JSON.parse(savedFeedback));
            } catch (e) {
                feedbackRegistry = new Map();
                SecureLogger.error('加载客服反馈登记数据失败');
            }

            // 【新增】加载店铺商品数据
            shopProducts = GM_getValue('shopProducts', []);
            const savedDescriptions = GM_getValue('shopProductDescriptions', '{}');
            try {
                shopProductDescriptions = new Map(Object.entries(JSON.parse(savedDescriptions)));
            } catch (e) {
                shopProductDescriptions = new Map();
                SecureLogger.error('加载商品描述数据失败');
            }

            // 新增：加载历史日志
            const savedHistoricalLogs = GM_getValue('historicalLogs', '[]');
            try {
                historicalLogs = JSON.parse(savedHistoricalLogs);
            } catch (e) {
                SecureLogger.error('加载历史日志失败');
                historicalLogs = [];
            }


            // ========== 恢复：加载AI会话上下文 ==========
            try {
                const savedContexts = GM_getValue('conversationContexts', '{}');
                const contextsObj = JSON.parse(savedContexts);
                conversationContexts = new Map(Object.entries(contextsObj));
            } catch (error) {
                SecureLogger.error('加载AI会话上下文失败');
                conversationContexts = new Map();
            }

            const today = new Date().toDateString();
            if (today !== savedDay) {
                todayReplies = 0;
                GM_setValue('todayReplies', 0);
                GM_setValue('lastSaveDay', today);
            } else {
                todayReplies = savedReplies;
            }

            lastReplyTime = savedTime;

            // 加载会话回复状态
            const savedReplyStatus = GM_getValue('sessionReplyStatus', '{}');
            try {
                sessionReplyStatus = new Map(Object.entries(JSON.parse(savedReplyStatus)));
            } catch (e) {
                sessionReplyStatus = new Map();
            }

        } catch (error) {
            SecureLogger.error(`加载保存数据失败: ${error.message}`);
            todayReplies = 0;
            lastReplyTime = '--:--:--';
            isAutoReplyEnabled = true;
            skipSystemReplies = true;
            customerServiceName = '';
            autoEndInterval = 0;

            processedMessages = new Map();
            sessionReplyStatus = new Map();
            shopProducts = []; // 新增
            shopProductDescriptions = new Map(); // 新增
            transferKeywords = ''; // 新增
            transferToAgent = ''; // 新增
            availableAgents = []; // 新增
            // 新增
            aiAgentKnowledgeBase = {
                conversations: [],
                dynamic_rules: []
            };
            trainingDialogMessages = [];
            feedbackRegistry = new Map();
        }
    }, '数据加载');


    // 核心修复：在脚本执行初期立即加载所有数据
    loadSavedData();


    // ========== 功能增强：状态监控 ==========
    // 添加状态监控事件并持久化
    const addStatusMonitorEvent = withErrorHandling(function(message, type = 'info') {
        const event = {
            time: new Date().toLocaleTimeString(),
            message: message,
            type: type
        };

        // 更新UI面板（只显示最近15条）
        statusMonitorEvents.unshift(event);
        if (statusMonitorEvents.length > 15) {
            statusMonitorEvents.pop();
        }
        updateStatusMonitorUI();

        // 更新并持久化历史日志（最多200条）
        historicalLogs.unshift(event);
        if (historicalLogs.length > 200) {
            historicalLogs.pop(); // 移除最旧的日志
        }
        // 保存到GM存储
        GM_setValue('historicalLogs', JSON.stringify(historicalLogs));
    }, '状态监控事件添加');

    // 更新状态监控UI
    const updateStatusMonitorUI = withErrorHandling(function() {
        // 在客服页面，更新主面板日志UI
        const monitorList = document.getElementById('statusMonitorList');
        if (!monitorList) return;

        monitorList.innerHTML = '';

        if (statusMonitorEvents.length === 0) {
            monitorList.innerHTML = '<div class="empty-templates">暂无状态日志</div>';
            return;
        }

        statusMonitorEvents.forEach(event => {
            const item = document.createElement('div');
            item.className = `status-monitor-item status-monitor-${event.type}`;
            item.innerHTML = `
                <div>${event.message}</div>
                <div class="status-monitor-time">${event.time}</div>
            `;
            monitorList.appendChild(item);
        });
    }, '状态监控UI更新');

    // 新增：渲染历史日志到弹窗
    const renderHistoricalLogs = withErrorHandling(function() {
        const container = document.getElementById('logHistoryContainer');
        if (!container) return;

        if (historicalLogs.length === 0) {
            container.innerHTML = '<div class="empty-templates">没有历史日志记录。</div>';
            return;
        }

        container.innerHTML = historicalLogs.map(event => `
            <div class="status-monitor-item status-monitor-${event.type}">
                <div>${event.message}</div>
                <div class="status-monitor-time">${event.time}</div>
            </div>
        `).join('');
    }, '渲染历史日志');


    /**
     * @description 【优化】处理订单查询的核心函数，支持智能识别订单卡片状态并给出相应回复
     * @param {string} sessionId 当前会话ID
     * @param {string} messageId 当前消息ID
     * @param {string} message 用户消息
     * @param {string} sessionName 当前会话名称
     */
    const handleOrderInquiry = withErrorHandling(async function(sessionId, messageId, message, sessionName) {
        addStatusMonitorEvent('进入订单查询处理流程...', 'info');
        processingSession = true; // 【修复】设置全局锁
        // 【修复】设置20秒超时定时器
        processingSessionTimeout = setTimeout(() => {
            if (processingSession) {
                processingSession = false; // 超时后解锁
                processingSessionTimeout = null;
                updateUserSessionList(sessionId, sessionName, 'completed'); // 标记为处理完成，即使是失败的
                addStatusMonitorEvent(`订单查询超时(20秒)，已解除会话锁定: ${sessionName}`, 'error');
            }
        }, 20000);

        // 【优化】检查是否有订单卡片 - 使用更精确的选择器
        const orderCard = document.querySelector('.order-card:not([style*="display: none"])') ||
                         document.querySelector('div[data-v-e15603ea].order-card') ||
                         document.querySelector('.order-list .order-card');

        if (orderCard && orderCard.offsetParent !== null) {
            addStatusMonitorEvent(`识别到订单卡片，开始智能分析订单状态...`, 'info');

            // 【新增】记录订单卡片的详细信息用于调试
            const orderNumElement = orderCard.querySelector('.order-id-wrap .num, .num');
            const orderStatusElement = orderCard.querySelector('.order-status, [class*="status"]');
            const orderNum = orderNumElement ? orderNumElement.textContent.trim() : '未知';
            const orderStatus = orderStatusElement ? orderStatusElement.textContent.trim() : '未知';
            addStatusMonitorEvent(`订单详情 - 订单号: ${orderNum}, 状态: ${orderStatus}`, 'info');

            // 【新增】智能识别订单状态并给出相应回复
            const smartOrderReply = await getSmartOrderReply(orderCard, message);
            await sendQuickReply(smartOrderReply, sessionId, messageId);

            // 【修复】卡片查询是即时的，完成后立即释放锁和超时
            processingSession = false;
            if(processingSessionTimeout) {
                clearTimeout(processingSessionTimeout);
                processingSessionTimeout = null;
            }
        } else {
            addStatusMonitorEvent('未发现订单卡片，检查消息中是否含手机号...', 'info');

            const phoneRegex = /\b(1\d{10})\b/;
            const fourDigitsRegex = /^\d{4}$/;
            const match = message.match(phoneRegex);
            let lastFourDigits = null;

            if (match && match[1]) {
                lastFourDigits = match[1].slice(-4);
            } else if (fourDigitsRegex.test(message.trim())) {
                lastFourDigits = message.trim();
            }

            if (lastFourDigits) {
                addStatusMonitorEvent(`从消息中提取到手机尾号: ${lastFourDigits}，启动新标签页查询...`, 'info');
                await sendQuickReply(`收到您的手机尾号【${lastFourDigits}】，正在为您查询订单，请稍候...`, sessionId, `temp_${messageId}`, true);
                await queryOrdersByPhoneInNewTab(lastFourDigits, sessionId, sessionName);
                // 注意：此处不释放锁 (processingSession = false)，锁将在 handleStorageChange 收到结果后或超时后释放。
            } else {
                // 【优化】没有订单卡片时，默认为咨询下单什么时候发货
                addStatusMonitorEvent('用户消息中未发现手机号，判断为咨询下单发货问题。', 'info');
                const shippingReply = "亲，您好！如果您是咨询下单后什么时候发货的话，我们一般会在您付款后的1-3个工作日内安排发货哦～具体发货时间会根据商品库存和物流情况而定。如果您已经下单，可以提供一下订单号或手机尾号，我帮您查询具体的发货时间呢！";
                await sendQuickReply(shippingReply, sessionId, messageId, true);

                // 【修复】发送回复后立即释放锁和超时
                processingSession = false;
                if(processingSessionTimeout) {
                    clearTimeout(processingSessionTimeout);
                    processingSessionTimeout = null;
                }
            }
        }
    }, '订单查询处理');

    /**
     * @description 【新增】智能分析订单卡片状态并生成相应回复
     * @param {HTMLElement} orderCard 订单卡片DOM元素
     * @param {string} userMessage 用户消息内容
     * @returns {Promise<string>} 返回智能生成的回复内容
     */
    const getSmartOrderReply = withErrorHandling(async function(orderCard, userMessage) {
        try {
            // 提取订单号
            const orderNumElement = orderCard.querySelector('.order-id-wrap .num, .num');
            const orderNumber = orderNumElement ? orderNumElement.textContent.trim() : '未知';
            const orderTail = orderNumber.length >= 4 ? orderNumber.slice(-4) : orderNumber;

            // 提取订单状态
            const orderStatusElement = orderCard.querySelector('.order-status, [class*="status"]');
            const orderStatus = orderStatusElement ? orderStatusElement.textContent.trim() : '';

            addStatusMonitorEvent(`订单状态识别: ${orderStatus}`, 'info');

            // 【优化】根据订单状态生成智能回复
            if (orderStatus.includes('已取消') || orderStatus.includes('取消')) {
                return `亲，您好！查询到您的订单(尾号${orderTail})已经取消，请问您遇到什么问题了呢？`;
            }
            else if (orderStatus.includes('待发货') || orderStatus.includes('待发') || orderStatus.includes('备货中') ||
                     orderStatus.includes('处理中') || orderStatus.includes('准备发货')) {
                // 【新增】尝试获取发货时间信息
                const deliveryTimeElement = orderCard.querySelector('.delivery-time');
                const deliveryTime = deliveryTimeElement ? deliveryTimeElement.textContent.trim() : '';

                let reply = `亲，您好！查询到您的订单(尾号${orderTail})待发货，已经为您备注加急发货，仓库正在处理中哦`;
                if (deliveryTime && deliveryTime.includes('前发货')) {
                    reply += `，预计${deliveryTime}`;
                }
                reply += `，谢谢您的支持！`;
                return reply;
            }
            else if (orderStatus.includes('已发货') || orderStatus.includes('运输中') || orderStatus.includes('配送中') ||
                     orderStatus.includes('派送中') || orderStatus.includes('在途')) {
                // 如果已发货，尝试获取物流信息
                const logisticsInfo = await fetchAndFormatOrderDetails();
                return logisticsInfo;
            }
            else if (orderStatus.includes('已完成') || orderStatus.includes('已收货') || orderStatus.includes('交易完成') ||
                     orderStatus.includes('已签收') || orderStatus.includes('完成')) {
                return `亲，您好！查询到您的订单(尾号${orderTail})已完成，感谢您的支持！如果商品有任何问题，请随时联系我们哦～`;
            }
            else if (orderStatus.includes('待付款') || orderStatus.includes('未付款') || orderStatus.includes('等待付款')) {
                return `亲，您好！查询到您的订单(尾号${orderTail})还未付款哦，请及时完成付款，我们会尽快为您安排发货的～`;
            }
            else if (orderStatus.includes('退款') || orderStatus.includes('退货')) {
                return `亲，您好！查询到您的订单(尾号${orderTail})正在处理退款/退货，我们会尽快为您处理，请耐心等待哦～`;
            }
            else {
                // 默认情况，尝试获取详细信息
                addStatusMonitorEvent(`未识别的订单状态: ${orderStatus}，尝试获取详细信息`, 'info');
                const detailedInfo = await fetchAndFormatOrderDetails();
                return detailedInfo;
            }

        } catch (error) {
            addStatusMonitorEvent(`智能订单分析失败: ${error.message}`, 'error');
            // 降级到原有的物流查询逻辑
            return await fetchAndFormatOrderDetails();
        }
    }, '智能订单状态分析');

    // =================================================================
    // 功能修复与优化: 核心逻辑与辅助函数
    // =================================================================

    /**
     * @description [核心修复] 智能判断最后消息状态，能正确处理系统自动接入消息。
     * @returns {object} 返回一个包含状态和信息(如果适用)的对象。
     */
    const checkLastMessageStatus = withErrorHandling(function() {
        const messageContainers = document.querySelectorAll('.chat-content .items-center');
        if (!messageContainers || messageContainers.length === 0) {
            return { status: 'NO_MESSAGES' };
        }
        for (let i = messageContainers.length - 1; i >= 0; i--) {
            const currentContainer = messageContainers[i];
            // 检查是否是客服发送的消息（在右边）
            if (currentContainer.classList.contains('justify-end')) {
                const csMessageBubble = currentContainer.querySelector('.bg-kf');
                if (csMessageBubble) {
                    const content = csMessageBubble.textContent.trim();
                    // 如果是系统自动接入语，则忽略，视为未回复
                    if (content.includes("正在接入中，人工客服马上为你提供服务")) {
                        continue; // 继续向上查找上一条消息
                    }
                    // 否则，视为客服已回复
                    return { status: 'REPLIED' };
                }
            }
            // 检查是否是用户发送的消息（在左边）
            const userMessageBubble = currentContainer.querySelector('.bg-user');
            if (userMessageBubble) {
                return {
                    status: 'UNREPLIED',
                    content: userMessageBubble.textContent.trim(),
                    element: currentContainer
                };
            }
        }
        // 如果遍历完都未找到明确的用户或客服消息（可能都是系统提示），则返回未知
        return { status: 'UNKNOWN' };
    }, '检查最后消息状态');

     /**
     * @description 顺序处理所有发现的会话，实现点击、检查、回复的闭环。
     * @param {Array<HTMLElement>} sessionItems - 从左侧会话列表获取的DOM元素数组。
     */
    async function processAllSessionsSequentially(sessionItems) {
        let repliedInThisCycle = false;

        for (const [index, sessionItem] of sessionItems.entries()) {
            if (!isAutoReplyEnabled) {
                addStatusMonitorEvent('扫描中止：自动回复已关闭。', 'warning');
                isAutoChecking = false;
                return;
            }

            // 【功能修复】如果在执行长任务（如订单查询），则暂停会话切换
            if (processingSession) {
                addStatusMonitorEvent('扫描暂停：正在执行订单查询，等待完成后继续...', 'info');
                // 等待锁被释放，使用Promise轮询
                await new Promise(resolve => {
                    const waitInterval = setInterval(() => {
                        if (!processingSession) {
                            clearInterval(waitInterval);
                            resolve();
                        }
                    }, 500); // 每0.5秒检查一次
                });
                addStatusMonitorEvent('订单查询完成/超时，恢复会话扫描。', 'info');
            }


            const sessionId = getSessionId(sessionItem);
            const sessionName = getSessionName(sessionItem) || `会话 ${index + 1}`;

            // 如果当前会话正在查询中，则跳过本次点击和处理
            if(monitoredSessions.has(sessionId) && monitoredSessions.get(sessionId).status === 'pending') {
                addStatusMonitorEvent(`[${index + 1}/${sessionItems.length}] 跳过: ${sessionName} (状态: 查询中)`, 'info');
                continue;
            }

            monitoredSessions.set(sessionId, { element: sessionItem, name: sessionName, status: 'pending' });
            updateUserSessionList(sessionId, sessionName, 'pending');

            const delaySeconds = 1;
            addStatusMonitorEvent(`[${index + 1}/${sessionItems.length}] 检查: ${sessionName} (等待 ${delaySeconds}s 加载)`, 'info');

            sessionItem.click();
            await new Promise(resolve => setTimeout(resolve, delaySeconds * 1000));

            currentSessionId = sessionId;
            const lastMessage = checkLastMessageStatus();

            // 获取并记录用户最后回复时间
            const userLastReplyTime = getUserLastReplyTime();
            updateSessionLastReplyTime(sessionId, userLastReplyTime);

            if (lastMessage.status === 'UNREPLIED') {
                updateUserSessionList(sessionId, sessionName, 'unreplied');
                addStatusMonitorEvent(`检测到用户未回复，触发回复时显示：小梅花AI客服正在回复消息`, 'warning');
                repliedInThisCycle = true;

                const allMessages = document.querySelectorAll('.chat-content .items-center');
                const messageIndex = allMessages.length - 1;
                const messageId = generateMessageId(lastMessage.element, lastMessage.content, messageIndex);

                if (!processedMessages.has(sessionId)) {
                    processedMessages.set(sessionId, new Set());
                }
                const sessionProcessed = processedMessages.get(sessionId);

                if (sessionProcessed.has(messageId)) {
                    addStatusMonitorEvent(`消息 (ID: ${messageId}) 在本轮已处理过，跳过。`, 'info');
                } else {
                    sessionProcessed.add(messageId);
                    await processMessageContent(lastMessage.content, sessionId, messageId, sessionName);
                    // 每次回复后等待一下，模拟真人操作并给系统反应时间
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            } else {
                updateUserSessionList(sessionId, sessionName, 'completed');
                addStatusMonitorEvent(`${sessionName} 无需回复。状态: ${lastMessage.status}`, 'info');
            }
        }

        isAutoChecking = false;
        currentSessionId = null;

        if (repliedInThisCycle) {
            addStatusMonitorEvent('本轮扫描和回复已全部完成。', 'success');
        } else {
            addStatusMonitorEvent('本轮扫描完成，未发现需要回复的会话。', 'info');
        }

        // 【功能修复】在扫描结束后才决定是否刷新
        // 不再依赖用户设置的 refreshInterval，直接触发默认刷新
        saveData();
        triggerPostScanRefresh('完整会话'); // 触发扫描后刷新
    }

     /**
     * @description [并发控制] 启动用户会话列表的顺序扫描，这是新版自动检查的核心入口。
     */
    const startAutoCheck = withErrorHandling(function() {
        if (isAutoChecking || isAutoEnding || isReplyingToFeedback || processingSession) {
            addStatusMonitorEvent('主扫描被跳过：已有其他任务在运行。', 'info');
            return;
        }

        isAutoChecking = true;
        addStatusMonitorEvent('启动新一轮用户会话列表扫描...', 'info');

        const userSessionListUI = document.getElementById('userSessionList');
        if (userSessionListUI) {
            userSessionListUI.innerHTML = '<div class="empty-templates">正在查找会话...</div>';
        }

        const sessionSelectors = [
            '.session-item-container', '.session-item', '.chat-item',
            '.kf-session-item', '[data-room-id]', '.conversation-item'
        ];
        let discoveredSessions = [];
        for (const selector of sessionSelectors) {
            const items = document.querySelectorAll(selector);
            if (items && items.length > 0) {
                discoveredSessions = Array.from(items);
                break;
            }
        }

        if (discoveredSessions.length === 0) {
            // 【功能修复】当未找到会话时，直接触发刷新流程
            addStatusMonitorEvent('未找到任何会话。', 'info');
            isAutoChecking = false;
            saveData();
            triggerPostScanRefresh('未发现会话，'); // 调用新的刷新函数
            return;
        }

        addStatusMonitorEvent(`发现 ${discoveredSessions.length} 个会话，开始逐一检查。`, 'info');
        processAllSessionsSequentially(discoveredSessions);
    }, '启动顺序会话扫描');

    /**
     * @description [核心流程优化] 处理消息内容，增加订单识别和AI智能体知识作为最高优先级
     * @param {string} content 用户消息内容
     * @param {string} sessionId 当前会话ID
     * @param {string} messageId 当前消息ID
     * @param {string} sessionName 当前会话名称
     */
    const processMessageContent = withErrorHandling(async function(content, sessionId, messageId, sessionName) {
        // 0. 最高优先级：检查是否为损坏/补发报告并登记
        if (await handleDamageReport(content, sessionId, messageId)) {
            return; // 如果是，处理后即结束，避免触发其他回复
        }

        // 1. 检查是否是订单查询
        if (isOrderQuery(content)) {
            await handleOrderInquiry(sessionId, messageId, content, sessionName);
            return;
        }

        // 1.5. 【新增】检查是否需要自动转接
        if (await checkForAndHandleTransfer(content)) {
            return; // 如果转接成功，则结束处理
        }

        const normalizedMsg = content.toLowerCase().replace(/\s+/g, '');

        // 2. 【V9.0.7 优化】检查是否匹配店铺商品关键词，并直接推送商品
        const matchedProduct = findShopProductMatch(normalizedMsg);
        if (matchedProduct) {
            await sendProductInvitationAndReply(matchedProduct, content, sessionId, messageId);
            return; // 匹配并处理后，终止后续流程
        }

        // 3. 检查AI智能体知识库 - 动态规则
        for (const rule of aiAgentKnowledgeBase.dynamic_rules) {
            const keywords = rule.trigger_keywords;
            if (keywords.some(kw => normalizedMsg.includes(kw.toLowerCase().replace(/\s+/g, '')))) {
                addStatusMonitorEvent(`匹配到动态规则: ${keywords[0]}...`, 'info');
                try {
                    const dataElement = document.querySelector(rule.selector);
                    const data = dataElement ? dataElement.innerText.trim() : '未知信息';
                    let reply = rule.template.replace(/\{DATA\}/g, data);
                    const orderNumEl = document.querySelector('.order-id-wrap .num, .num');
                    const orderNumber = orderNumEl ? orderNumEl.innerText.trim() : '（订单号未知）';
                    reply = reply.replace(/（订单号\）|{ORDER_ID}/g, orderNumber);
                    await sendQuickReply(reply, sessionId, messageId);
                    return;
                } catch(e) {
                    addStatusMonitorEvent(`执行动态规则失败: ${e.message}`, 'error');
                }
            }
        }



        // 5. 检查AI智能体知识库 - 对话、文档、网页（模糊匹配）
        const agentKnowledgeReply = findBestAgentKnowledgeReply(normalizedMsg);
        if(agentKnowledgeReply) {
             await sendQuickReply(agentKnowledgeReply, sessionId, messageId);
             return;
        }

        // ========== 恢复：AI智能回复功能 ==========
        // 6. 如果无任何匹配，且AI开启，则执行DeepSeek AI回复
        if (aiEnabled && aiApiKeys.length > 0 && !aiThinking) {
            if (aiReplyTimers.has(messageId)) {
                addStatusMonitorEvent(`AI回复 (ID: ${messageId}) 已在计划中，跳过。`, 'info');
                return;
            }
            const waitForDelay = (ms) => new Promise(res => setTimeout(res, ms));
            aiReplyTimers.set(messageId, true); // 标记此消息正在等待回复
            if (aiReplyDelay > 0) {
                addStatusMonitorEvent(`AI回复延迟 ${aiReplyDelay} 秒...`, 'info');
                await waitForDelay(aiReplyDelay * 1000);
            }
            const aiReply = await generateAIResponse(content, sessionId, null, sessionName);
            if (aiReply) {
                await sendQuickReply(aiReply, sessionId, messageId);
            }
            aiReplyTimers.delete(messageId); // 回复完成后删除标记
        }
    }, '消息内容处理');


    /**
     * @description 【优化】检查消息是否与订单/物流相关，增强发货咨询识别
     * @param {string} message 用户消息内容
     * @returns {boolean} 如果是订单相关查询则返回true
     */
    const isOrderQuery = withErrorHandling(function(message) {
        if (!message) return false;
        // 检查是否是纯11位数字的手机号，或4位数字的尾号
        if (/^1\d{10}$/.test(message.trim()) || /^\d{4}$/.test(message.trim())) {
            return true;
        }
        // 【优化】扩展订单和发货相关关键词
        const keywords = [
            // 订单相关
            '订单', '单号', '查一下订单', '查订单',
            // 发货相关
            '发货', '什么时候发', '什么时候发货', '多久发货', '几天发货', '发到哪了', '发了吗', '发了没', '发货了吗',
            '什么时候能发', '多长时间发货', '几号发货', '今天发货吗', '明天发货吗',
            // 物流相关
            '物流', '快递', '运单号', '到哪了', '到了吗', '收到了吗', '什么时候到',
            // 状态查询
            '进度', '状态', '处理了吗', '备货', '出库', '在路上'
        ];
        const normalizedMsg = message.toLowerCase().replace(/\s+/g, '');
        return keywords.some(kw => normalizedMsg.includes(kw.toLowerCase()));
    }, '订单查询关键词检查');


    /**
     * @description 后台获取订单物流详情并格式化为回复消息 (用于处理有订单卡片的情况)
     * @returns {Promise<string>} 返回一个Promise，最终解析为要发送给用户的字符串
     */
    const fetchAndFormatOrderDetails = withErrorHandling(function() {
        return new Promise(async (resolve) => {
            const orderCard = document.querySelector('.order-card, .order-id-wrap');
            if (!orderCard) {
                addStatusMonitorEvent('未找到订单卡片，无法查询物流', 'warning');
                resolve("抱歉，我没有在您的信息中看到订单，无法查询物流。");
                return;
            }
            const orderNumElement = orderCard.querySelector('.order-id-wrap .num, .num');
            if (!orderNumElement) {
                addStatusMonitorEvent('在订单卡片中未找到订单号元素', 'error');
                resolve("抱歉，无法自动识别您的订单号，请您提供一下订单号或截图，我为您查询。");
                return;
            }
            const orderNumber = orderNumElement.innerText.trim();
            if (!orderNumber) {
                addStatusMonitorEvent('提取的订单号为空', 'error');
                resolve("抱歉，自动识别您的订单号失败，请您提供一下订单号或截图，我为您查询。");
                return;
            }
            addStatusMonitorEvent(`识别到订单号: ${orderNumber}，准备查询物流详情。`, 'info');
            const orderDetailUrl = `/shop/order/detail?order_id=${orderNumber}`;
            addStatusMonitorEvent(`正在后台请求URL: ${orderDetailUrl}`, 'info');

            GM_xmlhttpRequest({
                method: "GET",
                url: orderDetailUrl,
                onload: function(response) {
                    if (response.status >= 200 && response.status < 300) {
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(response.responseText, "text/html");
                        // 查找物流信息的逻辑需要根据实际页面结构调整，以下为示例
                        const expressNameEl = doc.querySelector('.under_line .text');
                        const expressName = expressNameEl ? expressNameEl.innerText.trim() : '未知快递';
                        const trackingNumberTags = doc.querySelectorAll('.under_line .text');
                        let trackingNumber = '暂无单号';
                        if (trackingNumberTags.length > 1) {
                            trackingNumber = trackingNumberTags[1].innerText.replace('物流编号：', '').trim();
                        }
                        const latestStatusEl = doc.querySelector('.logistics_info .circle_green_tips:not(.unfinish)');
                        let latestStatusText = "暂无物流信息";
                        if (latestStatusEl) {
                            const statusTextEl = latestStatusEl.querySelector('.text:not(.bold)');
                            const statusTimeEl = latestStatusEl.querySelector('.label.gray.small_num');
                            const statusText = statusTextEl ? statusTextEl.innerText.trim() : '';
                            const statusTime = statusTimeEl ? statusTimeEl.innerText.trim() : '';
                            if (statusText && statusTime) {
                                latestStatusText = `最新状态: ${statusText} (${statusTime})`;
                            } else if (statusText) {
                                latestStatusText = `最新状态: ${statusText}`;
                            }
                        }
                        let reply = `亲，您好！查询到您的订单(尾号${orderNumber.slice(-4)})物流信息如下：\n`;
                        reply += `\n快递公司: ${expressName}`;
                        reply += `\n物流单号: ${trackingNumber}`;
                        reply += `\n${latestStatusText}`;
                        reply += `\n\n(为保护隐私，单号可能不完整，如需完整单号请告知)`;
                        addStatusMonitorEvent(`成功获取订单 ${orderNumber} 的物流信息`, 'success');
                        resolve(reply);
                    } else {
                        addStatusMonitorEvent(`后台请求订单详情页失败，状态码: ${response.status}`, 'error');
                        resolve(`抱歉，查询订单(尾号${orderNumber.slice(-4)})详情失败，可能是网络问题。请您稍后重试或在订单列表里手动查看。`);
                    }
                },
                onerror: function(error) {
                    SecureLogger.error(`GM_xmlhttpRequest error: ${error.error}`);
                    addStatusMonitorEvent(`后台请求订单详情页网络错误`, 'error');
                    resolve(`抱歉，查询订单(尾号${orderNumber.slice(-4)})时遇到网络错误，请您稍后再试。`);
                }
            });
        });
    }, '获取并格式化订单详情');


    // ========== 【新增与优化】店铺商品相关功能 ==========

    /**
     * @description [V9.0.7 修复并优化] 极速发送商品邀请下单卡片，并附带一句回复
     * @param {object} product - 匹配到的商品对象
     * @param {string} originalUserMessage - 原始用户消息
     * @param {string} sessionId - 当前会话ID
     * @param {string} messageId - 当前消息ID
     */
    const sendProductInvitationAndReply = withErrorHandling(async function(product, originalUserMessage, sessionId, messageId) {
        addStatusMonitorEvent(`开始为商品 [${product.title}] 推送邀请下单`, 'info');

        try {
            // 步骤 1: 确保右侧的 "商品" 标签页是激活状态
            const productTab = await waitForElement('.panel-tab li.tab-list-item', { textContent: '商品' });
            if (!productTab.classList.contains('active')) {
                productTab.click();
                addStatusMonitorEvent('已切换到商品标签页', 'info');
                // 等待面板内容加载完成
                await waitForElement('.product-panel input[placeholder*="搜索"]', { timeout: 5000 });
            }

            // 步骤 2: 使用搜索功能极速定位商品
            const searchInput = await waitForElement('.product-panel input[placeholder*="搜索"]');
            searchInput.click(); // 先聚焦
            await new Promise(r => setTimeout(r, 100)); // 短暂等待
            searchInput.value = ''; // 清空
            searchInput.dispatchEvent(new Event('input', { bubbles: true, composed: true }));
            await new Promise(r => setTimeout(r, 100)); // 短暂等待
            searchInput.value = product.id; // 输入ID
            searchInput.dispatchEvent(new Event('input', { bubbles: true, composed: true }));
            searchInput.dispatchEvent(new Event('change', { bubbles: true, composed: true })); // 触发change事件
            addStatusMonitorEvent(`已在商品搜索框中输入ID: ${product.id}`, 'info');

            // 步骤 3: 【核心修复】精准找到搜索结果中的商品卡片和 "邀请下单" 按钮
            const productListContainer = await waitForElement('.product-list');
            // 等待包含确切商品ID的元素出现
            const idElement = await waitForElement(`div`, {
                textContent: product.id,
                searchContext: productListContainer,
                timeout: 8000 // 给予充分的搜索响应时间
            });

            // 从ID元素向上查找到整个卡片
            const productCard = idElement.closest('.product-card');
            if (!productCard) {
                throw new Error(`找到了ID ${product.id} 的元素，但无法定位其父级的商品卡片。`);
            }
            addStatusMonitorEvent(`已精确定位到商品 [${product.title}] 的卡片`, 'info');

            // 在这个正确的卡片内查找邀请下单按钮
            const inviteButton = await waitForElement('button', {
                textContent: '邀请下单',
                searchContext: productCard
            });

            // 步骤 4: 点击邀请下单按钮
            inviteButton.click();
            addStatusMonitorEvent(`已成功点击 [${product.title}] 的邀请下单按钮`, 'success');
            await new Promise(r => setTimeout(r, 1000)); // 等待卡片发送动作完成

            // 步骤 5: 发送根据用户回复的话术
            const description = shopProductDescriptions.get(product.id) || `这款很不错`;
            const replyText = `您好，这款【${product.title}】${description}哦，您点击我发送的商品即可下单呢！`;

            // 使用现有函数发送文本消息，并标记为后续追问，避免再次触发 "还有什么可以帮您"
            await sendQuickReply(replyText, sessionId, `followup_${messageId}`, true);
            addStatusMonitorEvent(`已发送邀请下单后的跟进话术`, 'success');

            // 标记本次交互完成
            finalizeReply(`商品邀请: ${product.title}`, sessionId, messageId);

        } catch (error) {
            SecureLogger.error(`发送商品邀请下单流程出错: ${error.message}`);
            addStatusMonitorEvent(`发送商品邀请失败: ${error.message}`, 'error');
            // 如果自动化操作失败，提供一个备用方案，发送纯文本给用户
            const fallbackText = `亲，我为您推荐这款【${product.title}】，${shopProductDescriptions.get(product.id) || '它很受欢迎'}。您可以在我们店铺里搜索ID "${product.id}" 找到它哦。`;
            await sendQuickReply(fallbackText, sessionId, messageId);
        }
    }, 'V9.0.7-发送商品邀请及话术');


    /**
     * @description [V9.0.7 优化] 查找匹配的店铺商品，返回商品对象
     * @param {string} normalizedMsg - 标准化后的用户消息
     * @returns {object|null} - 返回匹配的商品对象或null
     */
    const findShopProductMatch = withErrorHandling(function(normalizedMsg) {
        // 遍历所有已设置描述的商品
        for (const [productId, description] of shopProductDescriptions.entries()) {
            if (!description || !description.trim()) continue;

            // 提取关键词。使用多种分隔符，并过滤掉空词
            const keywords = description.toLowerCase().split(/[，,。！？\s/|]+/).filter(k => k && k.length > 0);

            // 检查是否有任何一个关键词被用户消息包含
            if (keywords.some(keyword => normalizedMsg.includes(keyword))) {
                // 如果匹配，从主商品列表中找到完整的商品信息
                const product = shopProducts.find(p => p.id === productId);
                if (product) {
                    addStatusMonitorEvent(`关键词[${keywords.join(',')}]匹配到商品: ${product.title}`, 'info');
                    // 返回完整的商品对象，以便后续操作
                    return product;
                }
            }
        }
        // 如果没有找到匹配项，则返回null
        return null;
    }, 'V9.0.7-店铺商品关键词匹配');


    /**
     * @description 【优化】自动提取店铺所有商品信息（包含自动滚动加载）
     */
    const extractShopProducts = withErrorHandling(async function() {
        addStatusMonitorEvent('开始提取店铺商品信息...', 'info');
        const notification = document.getElementById('notification');
        const triggerBtn = document.getElementById('viewShopProductsBtn');
        const triggerLabel = triggerBtn ? triggerBtn.querySelector('.trigger-label') : null;

        if (triggerLabel) {
            triggerLabel.textContent = '正在提取，请稍候...';
        }

        try {
            // Step 1: Click "全部" tab
            addStatusMonitorEvent('步骤 1/6: 切换到 "全部" 会话列表...', 'info');
            const allSessionsTab = await waitForElement('.session-tab .tab-list-item', { textContent: '全部' });
            allSessionsTab.click();
            await new Promise(r => setTimeout(r, 1000));

            // Step 2: Select first user
            addStatusMonitorEvent('步骤 2/6: 选择第一个用户...', 'info');
            const firstUser = await waitForElement('#session-list-container .session-item-container:first-child');
            firstUser.click();
            await new Promise(r => setTimeout(r, 1000));

            // Step 3: Click "商品" tab in right panel
            addStatusMonitorEvent('步骤 3/6: 切换到 "商品" 标签页...', 'info');
            const productTab = await waitForElement('.panel-tab .tab-list-item', { textContent: '商品' });
            if (!productTab.classList.contains('active')) {
                productTab.click();
            }
            await new Promise(r => setTimeout(r, 1500)); // Wait for initial products to load

            // Step 4: Scroll to bottom to load all products
            addStatusMonitorEvent('步骤 4/6: 自动下滑加载全部商品...', 'info');
            if (triggerLabel) {
                 triggerLabel.textContent = '正在下滑加载...';
            }

            // ==========================================================
            // ===               FIXED SCROLLING LOGIC                ===
            // ==========================================================
            const scrollableContainer = await waitForElement('.product-panel-content.scrollbar-hide');
            let lastHeight = 0;
            let attempts = 0;
            const maxAttempts = 50;
            const scrollDelay = 1000;

            while (attempts < maxAttempts) {
                const productList = scrollableContainer.querySelector('.product-list');
                const currentProductCount = productList ? productList.querySelectorAll('.product-card').length : 0;
                addStatusMonitorEvent(`下滑加载中... (第 ${attempts + 1} 次, 已发现 ${currentProductCount} 个商品)`, 'info');

                lastHeight = scrollableContainer.scrollHeight;

                // Scroll the correct parent container to its bottom
                scrollableContainer.scrollTop = scrollableContainer.scrollHeight;
                scrollableContainer.dispatchEvent(new Event('scroll', { bubbles: true }));

                await new Promise(r => setTimeout(r, scrollDelay));

                if (scrollableContainer.scrollHeight === lastHeight) {
                    addStatusMonitorEvent('高度未增加，进行最后一次确认...', 'info');
                    scrollableContainer.scrollTop = scrollableContainer.scrollHeight;
                    scrollableContainer.dispatchEvent(new Event('scroll', { bubbles: true }));
                    await new Promise(r => setTimeout(r, scrollDelay * 1.5));

                    if (scrollableContainer.scrollHeight === lastHeight) {
                        addStatusMonitorEvent('已到达商品列表底部。', 'success');
                        break;
                    }
                }
                attempts++;
            }

            if (attempts >= maxAttempts) {
                addStatusMonitorEvent('已达到最大下滑次数，可能已加载完全部商品。', 'warning');
            }
            // ==========================================================
            // ===              END OF FIXED LOGIC                    ===
            // ==========================================================

            // Step 5: Extract product data
            addStatusMonitorEvent('步骤 5/6: 开始提取商品卡片信息...', 'info');
            if (triggerLabel) {
                 triggerLabel.textContent = '正在提取数据...';
            }
            // Extract from the now fully-loaded container
            const productCards = scrollableContainer.querySelectorAll('.product-list .product-card');

            if (productCards.length === 0) {
                showNotification(notification, '未找到任何商品信息。');
                addStatusMonitorEvent('提取失败：未在右侧面板找到商品卡片。', 'warning');
                if (triggerLabel) triggerLabel.textContent = '提取失败';
                return;
            }

            shopProducts = []; // Clear previous data
            productCards.forEach(card => {
                const idElement = card.querySelector('.product-id-wrap div:nth-child(2)');
                const titleElement = card.querySelector('.title');
                const priceElement = card.querySelector('.price');
                const imageElement = card.querySelector('.product-img');

                if (idElement && titleElement && priceElement) {
                    shopProducts.push({
                        id: idElement.textContent.trim(),
                        title: titleElement.textContent.trim(),
                        price: priceElement.textContent.trim(),
                        image: imageElement ? imageElement.src : ''
                    });
                }
            });

            // Step 6: Save and Render
            addStatusMonitorEvent(`步骤 6/6: 提取完成，共找到 ${shopProducts.length} 个商品。`, 'success');
            saveData();
            renderShopProducts();
            showNotification(notification, `成功提取 ${shopProducts.length} 个商品！`);

        } catch (error) {
            SecureLogger.error(`提取商品信息时出错: ${error.message}`);
            addStatusMonitorEvent(`提取商品失败: ${error.message}`, 'error');
            showNotification(notification, `提取商品失败: ${error.message}`);
            if (triggerLabel) triggerLabel.textContent = '提取出错';
        } finally {
             if (triggerLabel) triggerLabel.textContent = '已提取商品';
        }
    }, '提取店铺全部商品');

    /**
     * @description 【新增】处理从控制面板发起的商品操作（邀请下单/发商品）
     * @param {string} productId - 商品ID
     * @param {string} action - 操作类型 ('invite' 或 'send')
     */
    const handleProductPanelAction = withErrorHandling(async function(productId, action) {
        const actionText = action === 'invite' ? '邀请下单' : '发商品';
        addStatusMonitorEvent(`从控制面板触发 [${actionText}]，商品ID: ${productId}`, 'info');
        const notification = document.getElementById('notification');
        showNotification(notification, `正在发送 [${actionText}]...`);

        try {
            // 步骤 1: 确保右侧的 "商品" 标签页是激活状态
            const productTab = await waitForElement('.panel-tab li.tab-list-item', { textContent: '商品' });
            if (!productTab.classList.contains('active')) {
                productTab.click();
                addStatusMonitorEvent('已切换到商品标签页', 'info');
                await waitForElement('.product-panel input[placeholder*="搜索"]', { timeout: 5000 });
            }

            // 步骤 2: 使用搜索功能极速定位商品
            const searchInput = await waitForElement('.product-panel input[placeholder*="搜索"]');
            searchInput.value = productId;
            searchInput.dispatchEvent(new Event('input', { bubbles: true, composed: true }));
            addStatusMonitorEvent(`已在商品搜索框中输入ID: ${productId}`, 'info');

            // 步骤 3: 精准找到搜索结果中的商品卡片和按钮
            const productListContainer = await waitForElement('.product-list');
            // 等待包含确切商品ID的元素出现
            const idElement = await waitForElement(`div`, {
                textContent: productId,
                searchContext: productListContainer,
                timeout: 8000
            });

            const productCard = idElement.closest('.product-card');
            if (!productCard) {
                throw new Error(`找到了ID ${productId} 的元素，但无法定位其父级的商品卡片。`);
            }

            // 在这个正确的卡片内查找按钮
            // 注意：原始按钮的文本内容可能包含空格，使用 .includes() 更稳妥
            const allButtons = productCard.querySelectorAll('button.weui-desktop-btn');
            const actionButton = Array.from(allButtons).find(btn => btn.textContent.trim().includes(actionText));

            if (!actionButton) {
                throw new Error(`在商品卡片中未找到 "${actionText}" 按钮。`);
            }

            // 步骤 4: 点击按钮
            actionButton.click();
            addStatusMonitorEvent(`已成功点击 [${actionText}] 按钮`, 'success');
            showNotification(notification, `[${actionText}] 已发送！`);

        } catch (error) {
            SecureLogger.error(`执行商品操作 [${actionText}] 失败: ${error.message}`);
            addStatusMonitorEvent(`执行商品操作失败: ${error.message}`, 'error');
            showNotification(notification, `操作失败: ${error.message}`);
        }
    }, '控制面板商品操作');

    /**
     * @description 【优化】渲染店铺商品卡片，并为描述输入框和操作按钮绑定事件
     */
    const renderShopProducts = withErrorHandling(function() {
        const container = document.getElementById('shopProductsGrid');
        const statsElement = document.getElementById('shopProductsStats');
        const triggerCountElement = document.getElementById('shopProductsTriggerCount');

        if (!container) return;

        // 用于更新统计信息的辅助函数
        const updateStats = () => {
            const total = shopProducts.length;
            const withDescriptions = Array.from(shopProductDescriptions.values()).filter(desc => desc && desc.trim()).length;

            if (statsElement) {
                statsElement.textContent = `已提取 ${total} 个商品，其中 ${withDescriptions} 个已设置介绍`;
            }
            if (triggerCountElement) {
                triggerCountElement.textContent = `${total} 个`;
            }
        };

        if (shopProducts.length === 0) {
            container.innerHTML = '<div class="shop-products-empty">暂无商品数据，请在主面板点击"提取商品"按钮</div>';
            updateStats(); // 即使为空也要更新统计
            return;
        }

        container.innerHTML = shopProducts.map(product => `
            <div class="shop-product-card">
                <img src="${product.image || ''}" alt="${product.title}" class="shop-product-image" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA0MEg4MFY4MEg0MFY0MFoiIGZpbGw9IiNEREREREQiLz4KPC9zdmc+'">
                <div class="shop-product-title" title="${product.title}">${product.title}</div>
                <div class="shop-product-info">
                    <span class="shop-product-id">ID: ${product.id}</span>
                    <span class="shop-product-price">${product.price}</span>
                </div>
                <textarea
                    class="shop-product-description"
                    placeholder="输入产品介绍/关键词，用逗号分隔"
                    data-product-id="${product.id}"
                >${shopProductDescriptions.get(product.id) || ''}</textarea>
                <div class="shop-product-actions">
                    <button class="shop-product-btn btn-invite" data-product-id="${product.id}" data-action="invite">邀请下单</button>
                    <button class="shop-product-btn btn-send" data-product-id="${product.id}" data-action="send">发商品</button>
                </div>
            </div>
        `).join('');

        // 【修复】为每个介绍文本框绑定实时保存事件
        container.querySelectorAll('.shop-product-description').forEach(textarea => {
            textarea.addEventListener('input', (event) => {
                const productId = event.target.dataset.productId;
                const description = event.target.value;
                shopProductDescriptions.set(productId, description);
                GM_setValue('shopProductDescriptions', JSON.stringify(Object.fromEntries(shopProductDescriptions)));
                updateStats(); // 实时更新统计信息
            });
        });

        // 初始渲染时更新一次统计信息
        updateStats();
    }, '渲染店铺商品');




    // 点击结束按钮
    const clickEndButton = withErrorHandling(function() {
        // 因不同平台差异巨大，采用更广泛的选择器策略
        const endButtonSelectors = [
            'div.chat-title > div.right > div:nth-child(2)', // 优先使用特定结构
            '.btn-end', '.end-btn', '.close-chat-btn', // 常用类名
            'button[title*="结束对话"]', 'button[aria-label*="结束对话"]', // title或aria-label
        ];

        for (const selector of endButtonSelectors) {
            try {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) { // 检查元素是否可见
                    element.click();
                    return true;
                }
            } catch (e) {
                /* 忽略无效的选择器 */ }
        }

        // 最后的文本内容匹配策略
        const allButtons = document.querySelectorAll('button, div[role="button"]');
        for (const btn of allButtons) {
            if (btn.offsetParent !== null && (btn.textContent || "").trim().includes("结束")) {
                btn.click();
                return true;
            }
        }
        return false;
    }, '结束按钮点击');

    /**
     * @description [新] 自动结束功能的核心扫描函数。
     */
    const startAutoEndScan = withErrorHandling(async function() {
        if (isAutoChecking || isAutoEnding || isReplyingToFeedback || processingSession) {
            addStatusMonitorEvent('自动结束任务延迟：已有其他任务在运行。', 'warning');
            autoEndNextTriggerTimestamp = Date.now() + 15 * 1000;
            GM_setValue('autoEndNextTriggerTimestamp', autoEndNextTriggerTimestamp);
            setupAutoEndCountdown();
            return;
        }

        isAutoEnding = true;
        addStatusMonitorEvent('自动结束任务启动，开始扫描会话...', 'info');

        const sessionItems = Array.from(document.querySelectorAll('.session-item-container, .session-item, .chat-item'));
        if (sessionItems.length === 0) {
            addStatusMonitorEvent('[自动结束] 未找到任何会话，等待下一周期。', 'info');
        } else {
            for (const sessionItem of sessionItems) {
                const sessionId = getSessionId(sessionItem);
                const sessionName = getSessionName(sessionItem) || '未知用户';

                sessionItem.click();
                await new Promise(resolve => setTimeout(resolve, 2000));

                // 获取用户最后回复时间
                const userLastReplyTime = getUserLastReplyTime();

                // 更新会话列表中的时间显示
                updateSessionLastReplyTime(sessionId, userLastReplyTime);

                const lastMessage = checkLastMessageStatus();

                if (lastMessage.status === 'UNREPLIED') {
                    addStatusMonitorEvent(`[自动结束] 跳过 ${sessionName} (原因：用户有未回复消息)`, 'warning');
                } else {
                    // 检查是否应该结束对话
                    const shouldEndConversation = checkShouldEndConversation(userLastReplyTime);

                    if (shouldEndConversation) {
                        addStatusMonitorEvent(`[自动结束] 检查通过，尝试结束 ${sessionName} (用户最后回复: ${userLastReplyTime})`, 'info');
                        if (clickEndButton()) {
                            addStatusMonitorEvent(`[自动结束] 成功结束会话: ${sessionName}`, 'success');
                        } else {
                            addStatusMonitorEvent(`[自动结束] 结束会话失败: ${sessionName} (未找到结束按钮)`, 'error');
                        }
                    } else {
                        addStatusMonitorEvent(`[自动结束] 跳过 ${sessionName} (时间未到，用户最后回复: ${userLastReplyTime})`, 'info');
                    }
                }
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        addStatusMonitorEvent('自动结束扫描周期完成，新一轮倒计时即将开始。', 'success');
        isAutoEnding = false;

        autoEndNextTriggerTimestamp = Date.now() + autoEndInterval * 1000;
        GM_setValue('autoEndNextTriggerTimestamp', autoEndNextTriggerTimestamp);
        // 自动结束功能不再触发整页刷新，而是依赖自动刷新功能
        saveData();
    }, '自动结束扫描');

    /**
     * @description [新] 设置并管理自动结束功能的倒计时。
     */
    const setupAutoEndCountdown = withErrorHandling(function() {
        if (autoEndCountdownIntervalId) clearInterval(autoEndCountdownIntervalId);

        const countdownDisplay = document.getElementById('autoEndCountdownDisplay');

        const canRun = autoEndInterval > 0 && autoEndNextTriggerTimestamp > 0 && window.location.href.includes('/shop/kf');

        if (!canRun) {
            if (countdownDisplay) countdownDisplay.textContent = '下次检查倒计时: 已停止或暂停';
            return;
        }

        autoEndCountdownIntervalId = setInterval(() => {
            const now = Date.now();
            const remainingSeconds = Math.round((autoEndNextTriggerTimestamp - now) / 1000);

            if (remainingSeconds > 0) {
                if (countdownDisplay) countdownDisplay.textContent = `下次检查倒计时: ${remainingSeconds}秒`;
            } else {
                if (countdownDisplay) countdownDisplay.textContent = '正在检查...';

                clearInterval(autoEndCountdownIntervalId);
                startAutoEndScan();
            }
        }, 1000);

    }, '设置自动结束倒计时');


    const updateUserSessionList = withErrorHandling(function(sessionId, sessionName, status) {
        // 任何时候都尝试更新主扫描列表
        const mainSessionList = document.getElementById('userSessionList');
        if (mainSessionList) {
             updateSpecificSessionList(mainSessionList, sessionId, sessionName, status);
        }
    }, '更新用户会话列表');

    // 辅助函数，用于更新指定的列表
    function updateSpecificSessionList(listContainer, sessionId, sessionName, status) {
        let sessionItem = listContainer.querySelector(`[data-session-id="${sessionId}"]`);
        let statusElement;

        if (!sessionItem) {
            const monitored = monitoredSessions.get(sessionId);
            const nameToUse = sessionName || (monitored ? monitored.name : null);

            if (!nameToUse) return;

            sessionItem = document.createElement('div');
            sessionItem.className = 'user-session-item';
            sessionItem.setAttribute('data-session-id', sessionId);
            sessionItem.innerHTML = `
                <div class="user-session-info">
                    <div style="display: flex; flex-direction: column; flex: 1;">
                        <span class="user-session-name">${nameToUse}</span>
                        <span class="user-session-time" id="time-${listContainer.id}-${sessionId}" style="font-size: 11px; color: #888; margin-top: 2px;">上次回复: 检测中...</span>
                    </div>
                    <span class="user-session-status" id="status-${listContainer.id}-${sessionId}"></span>
                </div>
            `;
            const placeholder = listContainer.querySelector('.empty-templates');
            if (placeholder) placeholder.remove();
            listContainer.appendChild(sessionItem);
        }

        statusElement = sessionItem.querySelector(`#status-${listContainer.id}-${sessionId}`);

        if (statusElement) {
            let statusText = '';
            let statusClass = '';

            switch (status) {
                case 'pending':
                    // 【功能优化】区分扫描中和查询中
                    const isQuerying = processingSession && monitoredSessions.get(sessionId)?.status === 'pending';
                    statusText = isQuerying ? '查询中...' : '扫描中';
                    statusClass = 'user-session-pending';
                    break;
                case 'unreplied':
                    statusText = '待回复';
                    statusClass = 'user-session-active';
                    break;
                case 'completed':
                    statusText = '已处理';
                    statusClass = 'user-session-completed';
                    break;
                default:
                    statusText = '准备中';
                    statusClass = '';
            }
            statusElement.textContent = statusText;
            statusElement.className = `user-session-status ${statusClass}`;

            // 【功能优化】同时更新内存中的状态
            if (monitoredSessions.has(sessionId)) {
                monitoredSessions.get(sessionId).status = status;
            }
        }
    }

    // 检查会话是否有未回复消息（增强版）
    const hasUnrepliedMessages = withErrorHandling(function(sessionItem) {
        const sessionId = getSessionId(sessionItem);

        if (sessionReplyStatus.has(sessionId) && sessionReplyStatus.get(sessionId) === 'read_unreplied') {
            return true;
        }

        const lastMessage = getLastMessage(sessionItem);
        if (lastMessage && isCustomerMessage(lastMessage) && !isReplied(lastMessage)) {
            return true;
        }

        if (lastMessage) {
            const {
                content
            } = extractMessageContent(lastMessage);
            if (content && content.includes("正在接入中，人工客服马上为你提供服务")) {
                return true;
            }
        }

        const unreadSelectors = [
            '.unread', '.new-message', '.msg-unread',
            '[class*="unread"]', '.session-new',
            '.unread-count', '.unread-num', '.unread-badge',
            '.unread-dot', '.red-dot', '.new-msg-indicator'
        ];

        for (const selector of unreadSelectors) {
            if (sessionItem.querySelector(selector)) {
                return true;
            }
        }

        return false;
    }, '未回复消息检查');

    // 获取会话最后一条消息
    const getLastMessage = withErrorHandling(function(sessionItem) {
        const messageContainer = sessionItem.querySelector('.last-message, .msg-preview, .message-summary');
        if (messageContainer) {
            return messageContainer;
        }
        return null;
    }, '获取最后消息');

    // 检查消息是否已回复
    const isReplied = withErrorHandling(function(messageElement) {
        const sessionId = getSessionId(messageElement.closest('.session-item, .conversation-item'));
        if (!sessionId) return false;

        if (sessionReplyStatus.has(sessionId) && sessionReplyStatus.get(sessionId) === 'replied') {
            return true;
        }

        const statusIndicators = [
            '.replied', '.answered', '.done',
            '[class*="replied"]', '[class*="answered"]'
        ];

        for (const selector of statusIndicators) {
            if (messageElement.querySelector(selector)) {
                return true;
            }
        }

        return false;
    }, '检查消息回复状态');


    // 获取会话时间
    const getSessionTime = withErrorHandling(function(sessionItem) {
        const timeSelectors = [
            '.session-time', '.time', '.msg-time',
            '.last-time', '[class*="time"]'
        ];

        for (const selector of timeSelectors) {
            const element = sessionItem.querySelector(selector);
            if (element && element.textContent) {
                return element.textContent.trim();
            }
        }

        return null;
    }, '会话时间获取');

    // AI会话监控已删除

    // AI用户消息时间更新已删除

    // 新增：获取用户最后回复时间
    const getUserLastReplyTime = withErrorHandling(function() {
        const messageElements = document.querySelectorAll('.chat-content .items-center');
        let lastUserMessageTime = null;

        // 从最新消息开始向前查找用户的最后一条消息
        for (let i = messageElements.length - 1; i >= 0; i--) {
            const messageElement = messageElements[i];

            // 检查是否是用户消息（不是客服消息）
            if (isCustomerMessage(messageElement)) {
                // 尝试获取消息时间
                const timeElement = messageElement.querySelector('.msg-time, .time, [class*="time"]');
                if (timeElement && timeElement.textContent) {
                    lastUserMessageTime = timeElement.textContent.trim();
                    break;
                }

                // 如果没有找到时间元素，使用当前时间
                if (!lastUserMessageTime) {
                    lastUserMessageTime = new Date().toLocaleString();
                    break;
                }
            }
        }

        return lastUserMessageTime || '未知时间';
    }, '获取用户最后回复时间');

    // 新增：更新会话列表中的用户最后回复时间
    const updateSessionLastReplyTime = withErrorHandling(function(sessionId, lastReplyTime) {
        const mainSessionList = document.getElementById('userSessionList');
        if (mainSessionList) {
            const timeElement = mainSessionList.querySelector(`#time-userSessionList-${sessionId}`);
            if (timeElement) {
                timeElement.textContent = `上次回复: ${lastReplyTime}`;
            }
        }

        // 更新内存中的记录
        if (monitoredSessions.has(sessionId)) {
            const session = monitoredSessions.get(sessionId);
            session.lastUserReplyTime = lastReplyTime;
            monitoredSessions.set(sessionId, session);
        }
    }, '更新会话最后回复时间');

    // 新增：检查是否应该结束对话
    const checkShouldEndConversation = withErrorHandling(function(userLastReplyTime) {
        if (autoEndInterval <= 0) {
            return false; // 自动结束功能未启用
        }

        if (!userLastReplyTime || userLastReplyTime === '未知时间' || userLastReplyTime === '检测中...') {
            return false; // 无法确定时间
        }

        try {
            // 解析用户最后回复时间
            let lastReplyDate;

            // 尝试不同的时间格式解析
            if (userLastReplyTime.includes('年') || userLastReplyTime.includes('/') || userLastReplyTime.includes('-')) {
                // 完整日期时间格式
                lastReplyDate = new Date(userLastReplyTime);
            } else if (userLastReplyTime.includes(':')) {
                // 只有时间，假设是今天
                const today = new Date();
                const timeStr = userLastReplyTime.trim();
                const [hours, minutes, seconds] = timeStr.split(':').map(Number);
                lastReplyDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes || 0, seconds || 0);
            } else {
                return false; // 无法解析的时间格式
            }

            if (isNaN(lastReplyDate.getTime())) {
                return false; // 无效日期
            }

            const now = new Date();
            const timeDiffSeconds = Math.floor((now - lastReplyDate) / 1000);

            // 检查是否超过设定的自动结束时间
            return timeDiffSeconds >= autoEndInterval;

        } catch (error) {
            SecureLogger.warn(`解析用户最后回复时间失败: ${error.message}`);
            return false;
        }
    }, '检查是否应该结束对话');

    // ========== 新增：实时监控未读消息功能 ==========

    /**
     * @description 扫描会话列表中的未读消息标记
     * @returns {Array} 包含未读消息的会话元素数组
     */
    const scanUnreadMessages = withErrorHandling(function() {
        const sessionContainers = document.querySelectorAll('.session-item-container');
        const unreadSessions = [];

        sessionContainers.forEach(container => {
            // 查找未读消息标记
            const unreadBadge = container.querySelector('.unread-badge.bold');
            if (unreadBadge && unreadBadge.textContent && parseInt(unreadBadge.textContent) > 0) {
                const sessionId = container.getAttribute('data-room-id') || container.getAttribute('data-key');
                const userNickname = container.querySelector('.user-nickname')?.textContent || '未知用户';
                const unreadCount = parseInt(unreadBadge.textContent);

                unreadSessions.push({
                    element: container,
                    sessionId: sessionId,
                    userName: userNickname,
                    unreadCount: unreadCount,
                    timestamp: Date.now()
                });
            }
        });

        return unreadSessions;
    }, '扫描未读消息');

    /**
     * @description 处理单个未读会话
     * @param {Object} unreadSession 未读会话对象
     */
    const processUnreadSession = withErrorHandling(async function(unreadSession) {
        if (isProcessingUnread || !isAutoReplyEnabled) {
            return false;
        }

        isProcessingUnread = true;
        const { element, sessionId, userName, unreadCount } = unreadSession;

        addStatusMonitorEvent(`[实时监控] 发现未读消息: ${userName} (${unreadCount}条)`, 'warning');
        updateUserSessionList(sessionId, userName, 'pending');

        try {
            // 点击会话
            element.click();
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 获取并记录用户最后回复时间
            const userLastReplyTime = getUserLastReplyTime();
            updateSessionLastReplyTime(sessionId, userLastReplyTime);

            // 检查最后消息状态
            const lastMessage = checkLastMessageStatus();

            if (lastMessage.status === 'UNREPLIED') {
                updateUserSessionList(sessionId, userName, 'unreplied');
                addStatusMonitorEvent(`[实时监控] 开始处理 ${userName} 的未回复消息`, 'info');

                const allMessages = document.querySelectorAll('.chat-content .items-center');
                const messageIndex = allMessages.length - 1;
                const messageId = generateMessageId(lastMessage.element, lastMessage.content, messageIndex);

                if (!processedMessages.has(sessionId)) {
                    processedMessages.set(sessionId, new Set());
                }

                const sessionProcessed = processedMessages.get(sessionId);

                if (!sessionProcessed.has(messageId)) {
                    sessionProcessed.add(messageId);
                    await processMessageContent(lastMessage.content, sessionId, messageId, userName);
                    addStatusMonitorEvent(`[实时监控] 已回复 ${userName}`, 'success');
                } else {
                    addStatusMonitorEvent(`[实时监控] 消息已处理过，跳过: ${userName}`, 'info');
                }
            } else {
                updateUserSessionList(sessionId, userName, 'completed');
                addStatusMonitorEvent(`[实时监控] ${userName} 无需回复`, 'info');
            }

            return true;

        } catch (error) {
            addStatusMonitorEvent(`[实时监控] 处理 ${userName} 时出错: ${error.message}`, 'error');
            updateUserSessionList(sessionId, userName, 'completed');
            return false;
        } finally {
            isProcessingUnread = false;
        }
    }, '处理未读会话');

    /**
     * @description 启动实时未读消息监控
     */
    const startUnreadMonitoring = withErrorHandling(function() {
        if (unreadMonitorTimer) {
            clearInterval(unreadMonitorTimer);
        }

        addStatusMonitorEvent('[实时监控] 启动未读消息实时监控', 'success');

        unreadMonitorTimer = setInterval(async () => {
            if (!isAutoReplyEnabled || isProcessingUnread || isAutoEnding || isReplyingToFeedback || processingSession) {
                return;
            }

            const unreadSessions = scanUnreadMessages();

            if (unreadSessions.length > 0) {
                addStatusMonitorEvent(`[实时监控] 发现 ${unreadSessions.length} 个未读会话，开始处理`, 'info');

                // 按时间戳排序，优先处理较早的消息
                unreadSessions.sort((a, b) => a.timestamp - b.timestamp);

                // 逐个处理未读会话
                for (const session of unreadSessions) {
                    if (!isAutoReplyEnabled) break;

                    const success = await processUnreadSession(session);
                    if (success) {
                        // 处理完一个会话后稍作延迟
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
            }
        }, 2000); // 每2秒检查一次未读消息

    }, '启动未读消息监控');

    /**
     * @description 停止实时未读消息监控
     */
    const stopUnreadMonitoring = withErrorHandling(function() {
        if (unreadMonitorTimer) {
            clearInterval(unreadMonitorTimer);
            unreadMonitorTimer = null;
            addStatusMonitorEvent('[实时监控] 已停止未读消息监控', 'info');
        }
        isProcessingUnread = false;
        unreadQueue = [];
    }, '停止未读消息监控');

    /**
     * @description 更新实时监控状态显示
     */
    const updateRealtimeMonitorStatus = withErrorHandling(function() {
        const indicator = document.getElementById('realtimeMonitorIndicator');
        const statusText = document.getElementById('realtimeMonitorStatusText');

        if (indicator && statusText) {
            if (unreadMonitorTimer && isAutoReplyEnabled) {
                indicator.className = 'check-indicator active';
                statusText.textContent = '实时监控运行中';
                statusText.style.color = '#4CAF50';
            } else {
                indicator.className = 'check-indicator';
                statusText.textContent = '实时监控未启用';
                statusText.style.color = '#666';
            }
        }
    }, '更新实时监控状态');

    // AI回复时间更新已删除

    // AI功能已删除





    // AI功能已删除


    // AI功能已删除


    // ========== 新增：状态监控功能 ==========
    // AI恢复功能已删除










    // 初始化函数
    const initControlPanel = withErrorHandling(function() {
        // --- 以下是客服页面的通用面板初始化 ---
        if (document.getElementById('autoReplyPanel')) {
            if (!document.getElementById('aiTrainingDialog')) {
                document.body.insertAdjacentHTML('beforeend', aiTrainingDialogHTML);
                initTrainingDialog();
            }
            if (!document.getElementById('shopProductsModal')) {
                 document.body.insertAdjacentHTML('beforeend', shopProductsModalHTML);
            }
            // 新增：确保缩略图也存在
            if (!document.getElementById('collapsedThumbnail')) {
                document.body.insertAdjacentHTML('beforeend', collapsedThumbnailHTML);
            }
            autoExtractAndSetCSName();
            return;
        }

        document.body.insertAdjacentHTML('beforeend', panelHTML);
        document.body.insertAdjacentHTML('beforeend', collapsedThumbnailHTML);
        document.body.insertAdjacentHTML('beforeend', aiTrainingDialogHTML);
        document.body.insertAdjacentHTML('beforeend', apiKeysModalHTML);
        initUIComponents();
        initTrainingDialog();

        // 延迟初始化API密钥弹窗，确保DOM已完全加载
        setTimeout(() => {
            initApiKeysModal();
        }, 100);

        autoExtractAndSetCSName();

        addStatusMonitorEvent('控制面板初始化完成', 'success');

        // AI恢复调用已删除

    }, '控制面板初始化');

    // API密钥管理弹窗HTML
    const apiKeysModalHTML = `
        <div class="api-keys-modal" id="apiKeysModal" style="display: none;">
            <div class="api-keys-modal-content">
                <div class="api-keys-modal-header">
                    <h3>API密钥管理</h3>
                    <button class="modal-close-btn" id="closeApiKeysModal">&times;</button>
                </div>
                <div class="api-keys-modal-body">
                    <div class="api-keys-list" id="apiKeysModalList">
                        <!-- API密钥列表将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    `;

    // 创建控制面板HTML
    const panelHTML = `
    <div class="auto-reply-panel" id="autoReplyPanel">
        <div class="panel-wrapper" id="panelWrapper">
        <div class="panel-main-content">

            <!-- Column 1: Controls & Status -->
            <div class="panel-column">
                <div class="btn-container">
                    <button class="control-btn btn-enable" id="enableBtn" title="开启自动回复">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="white">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                        开启
                    </button>
                    <button class="control-btn btn-disable" id="disableBtn" title="停止自动回复">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="white">
                            <path d="M6 6h12v12H6z"/>
                        </svg>
                        停止
                    </button>
                </div>

                <div class="stats-section">
                    <div class="stats-row">
                        <div class="stats-label">运行状态：</div>
                        <div class="stats-value">
                            <span class="status-indicator" id="statusIndicator"></span>
                            <span id="statusValue">已停止</span>
                        </div>
                    </div>
                    <div class="stats-row">
                        <div class="stats-label">AI 回复状态：</div>
                        <div class="stats-value" id="aiReplyStatus">小梅花AI客服已禁用</div>
                    </div>
                    <div class="stats-row">
                        <div class="stats-label">今日回复：</div>
                        <div class="stats-value" id="replyCount">0</div>
                    </div>
                    <div class="stats-row">
                        <div class="stats-label">最后回复：</div>
                        <div class="stats-value" id="lastReplyTime">--:--:--</div>
                    </div>
                    <div class="stats-row">
                        <div class="stats-label">客服名字：</div>
                        <div class="stats-value">
                            <input type="text" id="csNameInput" class="cs-name-input" placeholder="自动提取中..." readonly>
                        </div>
                    </div>
                    <div class="stats-row">
                        <div class="stats-label">转接关键词：</div>
                        <div class="stats-value">
                            <input type="text" id="transferKeywordsInput" class="refresh-input" placeholder="多关键词用/分割">
                        </div>
                    </div>
                    <div class="stats-row">
                        <div class="stats-label">转接客服：</div>
                        <div class="stats-value" style="display: flex; gap: 5px;">
                            <select id="transferToAgentSelect" class="ai-model-select" style="flex-grow: 1;"></select>
                            <button id="refreshAgentsBtn" title="刷新客服列表" style="padding: 5px; border: 1px solid #ccc; background: #f0f0f0; border-radius: 4px; cursor: pointer;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#5a6c80"><path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/></svg>
                            </button>
                        </div>
                    </div>
                    <div class="stats-row" style="display: none;">
                        <div class="stats-label">跳过系统回复：</div>
                        <div class="stats-value">
                            <label class="switch">
                                <input type="checkbox" id="skipSystemToggle" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="collapsible-group" id="group4">
                    <div class="group-header">
                        <div class="group-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>
                            会话与状态监控
                        </div>
                        <svg class="group-toggle-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="#5a6c80"><path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/></svg>
                    </div>
                    <div class="group-content">
                        <div class="auto-check-section">
                            <div class="section-title">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#2196F3">
                                    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z"/>
                                </svg>
                                用户会话监控
                            </div>

                            <!-- 实时监控模式设置 -->
                            <div class="realtime-monitor-section" style="margin-bottom: 15px; padding: 10px; border: 1px solid #e1e8f0; border-radius: 8px; background: #e8f5e8;">
                                <div class="section-title" style="margin-bottom: 10px; font-size: 13px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="#4CAF50">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                    </svg>
                                    实时监控模式
                                </div>

                                <div class="check-status">
                                    <span class="check-indicator" id="realtimeMonitorIndicator"></span>
                                    <span id="realtimeMonitorStatusText">实时监控未启用</span>
                                </div>
                            </div>

                            <!-- 自动结束对话设置融合到这里 -->
                            <div class="auto-end-section" style="margin-bottom: 15px; padding: 10px; border: 1px solid #e1e8f0; border-radius: 8px; background: #f8f9ff;">
                                <div class="section-title" style="margin-bottom: 10px; font-size: 13px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="#FF6B6B">
                                        <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.75-1.23-4.5-2.67V8H12z"/>
                                    </svg>
                                    自动结束对话设置
                                </div>
                                <div class="refresh-input-container">
                                    <input type="number" min="0" max="3600" class="refresh-input" id="autoEndInput" placeholder="输入等待时间（秒）">
                                    <button class="btn-save-refresh" id="saveAutoEndBtn">保存</button>
                                    <button class="btn-stop-refresh" id="stopAutoEndBtn">停止</button>
                                </div>
                                <div class="refresh-status">
                                    <span class="refresh-indicator" id="autoEndIndicator"></span>
                                    <span id="autoEndStatusText">未启用自动结束</span>
                                </div>
                                <div class="auto-end-status" id="autoEndCountdownDisplay">下次检查倒计时: --</div>
                            </div>


                        </div>
                        <div class="user-session-section">
                            <div class="user-session-header">
                                <div class="section-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#8E24AA"><path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM12 14h-2v-2h2v2zm0-3h-2V9h2v2zm0-3h-2V6h2v2zm3 6h-2v-2h2v2zm0-3h-2V9h2v2zm0-3h-2V6h2v2zm3 6h-2v-2h2v2zm0-3h-2V9h2v2z"/></svg>
                                    当前会话列表
                                </div>
                            </div>
                            <div class="user-session-list" id="userSessionList">
                                <div class="empty-templates">等待扫描...</div>
                            </div>
                        </div>

                        <div class="status-monitor-section">
                            <div class="status-monitor-header">
                                <div class="section-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#2196F3"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>
                                    状态监控
                                </div>
                                <div class="header-buttons">
                                    <button class="session-action-btn" id="viewLogHistoryBtn" title="查看历史日志">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="#2196F3"><path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/></svg>
                                    </button>
                                    <button class="session-action-btn" id="clearStatusBtn" title="清空日志">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="#FA5151"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
                                    </button>
                                </div>
                            </div>
                            <div class="status-monitor-list" id="statusMonitorList">
                                <div class="empty-templates">暂无状态日志</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Column 2: AI & Knowledge -->
            <div class="panel-column">
                <div class="collapsible-group" id="group3">
                    <div class="group-header">
                        <div class="group-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM9.5 16.5v-2h-2v-2h2v-2h2v2h2v2h-2v2h-2zm6.75-2.25c.42 0 .8.16 1.09.41.29.25.44.6.46 1.04v.05h-2.01v-.05c-.02-.44.13-.79.43-1.04.3-.25.68-.41 1.03-.41zm2-3.25c.41 0 .78.16 1.06.41.28.25.42.6.44 1.04v.05h-2v-.05c-.02-.44.13-.79.42-1.04.29-.25.65-.41 1.08-.41z"/></svg>
                            AI 与知识库
                        </div>
                        <svg class="group-toggle-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="#5a6c80"><path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/></svg>
                    </div>
                    <div class="group-content">
                        <div class="ai-section">
                            <div class="section-title">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#FF6B6B">
                                    <path d="M20 2H4c-1.1 0-1.99.9-2 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z"/>
                                </svg>
                                AI智能回复设置
                            </div>
                            <div class="ai-settings-row">
                                <div class="ai-toggle-container">
                                    <label class="switch">
                                        <input type="checkbox" id="aiToggle">
                                        <span class="slider"></span>
                                    </label>
                                    <span>启用AI回复</span>
                                </div>
                            </div>
                            <!-- 【优化2】内置API密钥输入区域 -->
                            <div class="form-row">
                                <div class="form-label">内置密钥：</div>
                                <div class="builtin-api-keys-container">
                                    <textarea class="builtin-api-input" id="builtinApiKeysInput" placeholder="请在这里输入您的API密钥，一行一个：&#10;sk-your-api-key-here-1&#10;sk-your-api-key-here-2" rows="3"></textarea>
                                    <button type="button" class="control-btn btn-save-builtin-api" id="saveBuiltinApiKeysBtn">应用内置密钥</button>
                                    <div class="builtin-api-info">
                                        <small>内置密钥将自动同步到下方的保存密钥列表中，并可正常使用</small>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-label">API密钥：</div>
                                <div class="api-keys-container">
                                    <div class="api-key-input-row">
                                        <input type="password" class="ai-api-input" id="aiApiKeyInput" placeholder="输入DeepSeek API密钥">
                                        <button type="button" class="control-btn btn-save-api" id="saveApiKeyBtn">保存</button>
                                    </div>
                                    <div class="api-keys-status" id="apiKeysStatus">
                                        <!-- API密钥状态显示区域 -->
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-label">模型：</div>
                                <select class="ai-model-select" id="aiModelSelect">
                                    <option value="deepseek-chat">DeepSeek Chat</option>
                                    <option value="deepseek-reasoner">DeepSeek-R1-0528</option>
                                </select>
                            </div>
                            <div class="deep-thinking-section">
                                <div class="deep-thinking-toggle">
                                    <label class="switch">
                                        <input type="checkbox" id="deepThinkingToggle">
                                        <span class="slider"></span>
                                    </label>
                                    <span>深度思考 (R1)</span>
                                </div>
                                <div class="deep-thinking-info">仅对DeepSeek-R1-0528模型有效</div>
                            </div>

                            <div class="form-row">
                                <div class="form-label">回复延迟：</div>
                                <input type="number" min="0" max="60" class="form-input" id="aiReplyDelayInput" placeholder="秒（0为立即回复）">
                            </div>
                            <div class="ai-status">
                                <span class="ai-status-indicator" id="aiStatusIndicator"></span>
                                <span id="aiStatusText">AI已禁用</span>
                            </div>
                            <div class="ai-info-tip">
                                当用户消息不匹配任何关键词时，使用AI生成回复
                            </div>

                        </div>
                        <div class="ai-agent-knowledge-section">
                            <div class="section-title">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#f6ad55"><path d="M14.25 2.25h-8.5C4.784 2.25 4 3.034 4 3.75v16.5c0 .716.784 1.5 1.75 1.5h12.5c.966 0 1.75-.784 1.75-1.5V8.5L14.25 2.25zM18 20H6V4h8v5h5v11zM8 17h8v-2H8v2zm0-4h8v-2H8v2z"/></svg>
                                AI智能体知识库
                            </div>
                            <div class="btn-container">
                                <button class="control-btn btn-ai-train" id="openAITrainingDialogBtn">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="white"><path d="M21.582 17.346l-5.078-5.079c.92-1.263 1.496-2.793 1.496-4.45A8.823 8.823 0 009.183 0a8.823 8.823 0 00-8.817 8.817c0 4.867 3.95 8.817 8.817 8.817 1.658 0 3.187-.577 4.45-1.497l5.078 5.078c.58.58 1.52.58 2.1 0l.96-.96a1.489 1.489 0 000-2.1zM9.183 15.633c-3.76 0-6.817-3.056-6.817-6.816S5.423 2 9.183 2s6.817 3.057 6.817 6.817-3.057 6.816-6.817 6.816z"/></svg>
                                    AI智能体训练
                                </button>
                                <button class="control-btn btn-ai-clear" id="clearAIKnowledgeBtn">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="white"><path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/></svg>
                                    清除AI训练内容
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Column 3: App-specific & Collapse Button -->
            <div class="panel-column">
                <div class="collapsible-group" id="group5">
                    <div class="group-header">
                        <div class="group-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/></svg>
                            客服自动登记反馈
                        </div>
                        <svg class="group-toggle-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="#5a6c80"><path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/></svg>
                    </div>
                    <div class="group-content">
                        <div class="feedback-section">
                            <div class="section-title">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#38f9d7">
                                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                </svg>
                                AI客服登记反馈 (<span id="feedbackRegistryCount">0</span>条待处理)
                            </div>
                            <div class="btn-container">
                                <button class="control-btn btn-feedback-download" id="downloadFeedbackBtn">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M5 20h14v-2H5v2zm7-18L5.33 9h3.17v6h7V9h3.17L12 2z"/></svg>
                                    下载登记表
                                </button>
                                <button class="control-btn btn-feedback-upload" id="uploadFeedbackBtn">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16h6v-6h4l-7-7-7 7h4v6zm-4 2h14v-2H5v2z"/></svg>
                                    上传处理表
                                </button>
                            </div>
                            <div class="btn-container">
                                <button class="control-btn btn-feedback-reply" id="replyToFeedbackUsersBtn" style="flex: 100%;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M22 4c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4z"/></svg>
                                    回复登记的用户
                                </button>
                            </div>
                            <div class="ai-info-tip">
                                AI自动识别用户反馈的损坏/补发问题并登记。下载表格处理后上传，再点击回复按钮即可通知用户处理结果。
                            </div>
                        </div>
                    </div>
                </div>

                <div class="collapsible-group" id="group6">
                    <div class="group-header">
                        <div class="group-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/></svg>
                            店铺全部商品设置
                        </div>
                        <svg class="group-toggle-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="#5a6c80"><path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/></svg>
                    </div>
                    <div class="group-content">
                        <div class="shop-products-section">
                            <div class="section-title">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#667eea">
                                    <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
                                </svg>
                                店铺商品管理
                            </div>
                            <div class="btn-container">
                                <button class="control-btn btn-shop-products" id="extractShopProductsBtn">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="white">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    提取商品
                                </button>
                                <button class="control-btn btn-refresh" id="clearShopProductsBtn">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="white">
                                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                    </svg>
                                    清空商品
                                </button>
                            </div>
                             <!-- 【优化】新增的商品弹窗触发器 -->
                            <div class="shop-products-view-trigger" id="viewShopProductsBtn">
                                <div class="trigger-info">
                                    <span class="trigger-label" id="shopProductsTriggerLabel">已提取商品</span>
                                    <span class="trigger-count" id="shopProductsTriggerCount">0 个</span>
                                </div>
                                <div class="trigger-action">
                                    点击查看提取的商品
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/></svg>
                                </div>
                            </div>
                            <div class="ai-info-tip">
                                点击"提取商品"自动获取店铺所有商品信息，在弹窗的每个商品卡片下方输入产品介绍/关键词(用逗号分隔)，用户触发时会自动推送商品和话术。
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- 优化：将收起按钮移动到面板外部，完全独立定位 -->
    <button class="collapse-trigger" id="collapseTrigger" title="收起面板">收起知识库</button>

        <input type="file" id="fileInput" class="file-input" accept=".xlsx, .xls">


        <input type="file" id="feedbackFileInput" class="file-input" accept=".xlsx, .xls" style="display: none;">
        <div class="notification" id="notification">操作成功！</div>

    <!-- 【优化】新增的商品弹窗 -->
    <div id="shopProductsModal" class="shop-products-modal">
        <div class="shop-products-modal-content">
            <div class="shop-products-modal-header">
                <h2>店铺商品管理</h2>
                <button id="closeShopProductsModalBtn">&times;</button>
            </div>
            <div class="shop-products-modal-body">
                 <div class="shop-products-grid" id="shopProductsGrid">
                    <!-- Products will be rendered here by JS -->
                 </div>
            </div>
            <div class="shop-products-modal-footer">
                <div class="shop-products-stats" id="shopProductsStats">
                    <!-- Stats will be rendered here by JS -->
                </div>
            </div>
        </div>
    </div>

    <div id="logHistoryModal" class="log-history-modal">
        <div class="log-history-modal-content">
            <div class="log-history-modal-header">
                <h2>状态监控历史日志</h2>
                <button id="closeLogHistoryModalBtn">&times;</button>
            </div>
            <div class="log-history-modal-body" id="logHistoryContainer">
            </div>
        </div>
    </div>
    `;

    // ========== 新增：收起后的缩略图HTML ==========
    const collapsedThumbnailHTML = `
    <div class="collapsed-thumbnail" id="collapsedThumbnail" style="display: none;">
        <div class="thumbnail-title">小梅花AI智能客服</div>
        <div class="thumbnail-subtitle">微信小店知识库</div>
    </div>
    `;

    // ========== 新增：AI智能体训练对话框HTML ==========
    const aiTrainingDialogHTML = `
    <div class="ai-training-dialog" id="aiTrainingDialog" style="display: none;">
        <div class="ai-training-dialog-header" id="aiTrainingDialogHeader">
            <span>AI 智能体训练</span>
            <div class="header-controls">
                <button id="searchToggleBtn" class="header-btn" title="搜索对话内容">🔍</button>
                <button id="closeTrainingDialogBtn" class="header-btn">&times;</button>
            </div>
        </div>
        <div class="search-bar" id="searchBar" style="display: none;">
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="搜索对话内容..." />
                <div class="search-controls">
                    <button id="searchPrevBtn" title="上一个">↑</button>
                    <button id="searchNextBtn" title="下一个">↓</button>
                    <span id="searchResults">0/0</span>
                    <button id="searchCloseBtn" title="关闭搜索">&times;</button>
                </div>
            </div>
        </div>
        <div class="ai-training-dialog-body">
            <div class="ai-training-messages" id="aiTrainingMessages">
            </div>
            <div class="ai-training-input-area">
                <div class="qa-training-section">
                    <button id="qaTrainingBtn" class="qa-training-btn" title="快速插入问答训练模板">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="white">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        问答训练
                    </button>
                </div>
                <div class="ai-training-input-container">
                    <button id="uploadFileBtn" class="upload-file-btn" title="上传文件">+</button>
                    <textarea id="trainingInput" placeholder="在此输入内容或上传文件进行训练"></textarea>
                    <button id="sendTrainingMsgBtn">➤</button>
                </div>
                <input type="file" id="trainingFileInput" class="file-input" accept="image/*,video/*,.doc,.docx,.pdf,.xls,.xlsx,.txt" multiple style="display: none;">
                <div id="uploadedFilesPreview" class="uploaded-files-preview"></div>
            </div>
        </div>
    </div>
    `;

    /**
     * @description 【功能修复】自动提取并设置客服名字为只读
     */
    const autoExtractAndSetCSName = withErrorHandling(function() {
        if (!window.location.href.includes('/shop/kf')) return;

        const csNameInput = document.getElementById('csNameInput');
        if (!csNameInput) return; // 如果面板不存在，则不执行

        const maxRetries = 15;
        let attempt = 0;

        const tryToExtract = () => {
            if (attempt >= maxRetries) {
                addStatusMonitorEvent('自动提取客服名字超时，请检查页面元素。', 'warning');
                 csNameInput.placeholder = "提取失败,请刷新";
                return;
            }

            const nameElement = document.querySelector('div.name-wrap p.account-name');

            if (nameElement && nameElement.textContent.trim()) {
                const extractedName = nameElement.textContent.trim();
                if (csNameInput.value !== extractedName) {
                    csNameInput.value = extractedName;
                    customerServiceName = extractedName;
                    GM_setValue('customerServiceName', customerServiceName);
                    showNotification(document.getElementById('notification'), `已自动提取客服名字: ${extractedName}`);
                    addStatusMonitorEvent(`已自动提取并设置客服名字: ${extractedName}`, 'success');
                    saveData();
                }
            } else {
                attempt++;
                setTimeout(tryToExtract, 500);
            }
        };

        tryToExtract();
    }, '自动提取客服名字');


    // 初始化UI组件
    const initUIComponents = withErrorHandling(function() {
        const autoReplyPanel = document.getElementById('autoReplyPanel');
        const collapsedThumbnail = document.getElementById('collapsedThumbnail');

        if (autoReplyPanel) autoReplyPanel.style.display = 'flex';

        const elements = {
            statusIndicator: document.getElementById('statusIndicator'),
            statusValue: document.getElementById('statusValue'),
            aiReplyStatus: document.getElementById('aiReplyStatus'),
            replyCount: document.getElementById('replyCount'),
            lastReplyTimeElement: document.getElementById('lastReplyTime'),
            fileInput: document.getElementById('fileInput'),
            productKnowledgeFileInput: document.getElementById('productKnowledgeFileInput'),
            productInfoFileInput: document.getElementById('productInfoFileInput'),
            forbiddenKnowledgeFileInput: document.getElementById('forbiddenKnowledgeFileInput'),
            aiTrainingFileInput: document.getElementById('aiTrainingFileInput'),
            notification: document.getElementById('notification'),
            enableBtn: document.getElementById('enableBtn'),
            disableBtn: document.getElementById('disableBtn'),

            csNameInput: document.getElementById('csNameInput'),
            autoEndInput: document.getElementById('autoEndInput'),
            saveAutoEndBtn: document.getElementById('saveAutoEndBtn'),
            stopAutoEndBtn: document.getElementById('stopAutoEndBtn'),
            autoEndIndicator: document.getElementById('autoEndIndicator'),
            autoEndStatusText: document.getElementById('autoEndStatusText'),
            autoEndCountdownDisplay: document.getElementById('autoEndCountdownDisplay'),
            realtimeMonitorIndicator: document.getElementById('realtimeMonitorIndicator'),
            realtimeMonitorStatusText: document.getElementById('realtimeMonitorStatusText'),
            // ========== 恢复：AI相关元素 ==========
            aiToggle: document.getElementById('aiToggle'),
            // 【优化2】内置API密钥相关元素
            builtinApiKeysInput: document.getElementById('builtinApiKeysInput'),
            saveBuiltinApiKeysBtn: document.getElementById('saveBuiltinApiKeysBtn'),
            aiApiKeyInput: document.getElementById('aiApiKeyInput'),
            saveApiKeyBtn: document.getElementById('saveApiKeyBtn'),
            savedApiKeys: document.getElementById('savedApiKeys'),
            aiModelSelect: document.getElementById('aiModelSelect'),
            deepThinkingToggle: document.getElementById('deepThinkingToggle'),
            aiReplyDelayInput: document.getElementById('aiReplyDelayInput'),
            aiStatusIndicator: document.getElementById('aiStatusIndicator'),
            aiStatusText: document.getElementById('aiStatusText'),
            clearStatusBtn: document.getElementById('clearStatusBtn'),
            statusMonitorList: document.getElementById('statusMonitorList'),


            viewLogHistoryBtn: document.getElementById('viewLogHistoryBtn'),
            logHistoryModal: document.getElementById('logHistoryModal'),
            closeLogHistoryModalBtn: document.getElementById('closeLogHistoryModalBtn'),
            openAITrainingDialogBtn: document.getElementById('openAITrainingDialogBtn'),
            clearAIKnowledgeBtn: document.getElementById('clearAIKnowledgeBtn'),
            downloadFeedbackBtn: document.getElementById('downloadFeedbackBtn'),
            uploadFeedbackBtn: document.getElementById('uploadFeedbackBtn'),
            replyToFeedbackUsersBtn: document.getElementById('replyToFeedbackUsersBtn'),
            feedbackFileInput: document.getElementById('feedbackFileInput'),
            feedbackRegistryCount: document.getElementById('feedbackRegistryCount'),
            // 【新增】店铺商品相关元素
            extractShopProductsBtn: document.getElementById('extractShopProductsBtn'),
            clearShopProductsBtn: document.getElementById('clearShopProductsBtn'),
            shopProductsModal: document.getElementById('shopProductsModal'),
            closeShopProductsModalBtn: document.getElementById('closeShopProductsModalBtn'),
            viewShopProductsBtn: document.getElementById('viewShopProductsBtn'),
            shopProductsGrid: document.getElementById('shopProductsGrid'),
            shopProductsStats: document.getElementById('shopProductsStats'),
            shopProductsTriggerCount: document.getElementById('shopProductsTriggerCount'),
            shopProductsTriggerLabel: document.getElementById('shopProductsTriggerLabel'),
            // 【新增】会话转接相关元素
            transferKeywordsInput: document.getElementById('transferKeywordsInput'),
            transferToAgentSelect: document.getElementById('transferToAgentSelect'),
            refreshAgentsBtn: document.getElementById('refreshAgentsBtn'),
            // 【新增】收起/展开相关元素
            collapseTrigger: document.getElementById('collapseTrigger'),
            collapsedThumbnail: document.getElementById('collapsedThumbnail'),
        };

        // ========== 新增：收起/展开功能事件绑定 ==========
        if (elements.collapseTrigger) {
            elements.collapseTrigger.addEventListener('click', () => {
                // 【优化】收起控制面板
                if (autoReplyPanel) autoReplyPanel.style.display = 'none';
                if (collapsedThumbnail) collapsedThumbnail.style.display = 'flex';

                // 【新增】同时收起所有相关窗口
                const aiTrainingDialog = document.getElementById('aiTrainingDialog');
                const logHistoryModal = document.getElementById('logHistoryModal');
                const shopProductsModal = document.getElementById('shopProductsModal');

                if (aiTrainingDialog) aiTrainingDialog.style.display = 'none';
                if (logHistoryModal) logHistoryModal.style.display = 'none';
                if (shopProductsModal) shopProductsModal.style.display = 'none';

                // 隐藏收起按钮，不显示展开按钮
                elements.collapseTrigger.style.display = 'none';

                // 【新增】添加状态监控日志
                addStatusMonitorEvent('所有面板窗口已收起', 'info');
            });
        }
        if (elements.collapsedThumbnail) {
            elements.collapsedThumbnail.addEventListener('click', () => {
                if (collapsedThumbnail) collapsedThumbnail.style.display = 'none';
                if (autoReplyPanel) autoReplyPanel.style.display = 'flex';
                // 重新显示收起按钮
                if (elements.collapseTrigger) {
                    elements.collapseTrigger.style.display = 'flex';
                    elements.collapseTrigger.textContent = '收起知识库';
                    elements.collapseTrigger.title = '收起面板';
                }
            });
        }
        // =================================================

        document.querySelectorAll('.collapsible-group').forEach(group => {
            const header = group.querySelector('.group-header');
            const groupId = group.id;

            // 根据保存的状态或默认值设置折叠状态
            if (groupStates[groupId] === true) {
                group.classList.add('collapsed');
            } else {
                 group.classList.remove('collapsed');
            }

            if (header) {
                header.addEventListener('click', () => {
                    const isCollapsed = group.classList.toggle('collapsed');
                    groupStates[groupId] = isCollapsed;
                    saveData();
                });
            }
        });

        // 【新增】店铺商品功能事件绑定
        if (elements.extractShopProductsBtn) {
            elements.extractShopProductsBtn.addEventListener('click', () => {
                elements.extractShopProductsBtn.disabled = true;
                elements.extractShopProductsBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="white" class="spinner-anim"><path d="M12 4V2A10 10 0 0 0 2 12h2A8 8 0 0 1 12 4z"/></svg>
                    提取中...
                `;

                extractShopProducts().finally(() => {
                    elements.extractShopProductsBtn.disabled = false;
                    elements.extractShopProductsBtn.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="white">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        提取商品
                    `;
                });
            });
        }

        if (elements.clearShopProductsBtn) {
            elements.clearShopProductsBtn.addEventListener('click', () => {
                if (window.confirm('确定要清空所有店铺商品数据吗？此操作不可撤销。')) {
                    shopProducts = [];
                    shopProductDescriptions.clear();
                    GM_setValue('shopProducts', shopProducts);
                    GM_setValue('shopProductDescriptions', JSON.stringify(Object.fromEntries(shopProductDescriptions)));
                    renderShopProducts();
                    showNotification(elements.notification, '店铺商品数据已清空');
                    addStatusMonitorEvent('店铺商品数据已清空', 'info');
                    saveData();
                }
            });
        }

        // 【新增和优化】为商品网格添加事件委托，处理按钮点击
        if (elements.shopProductsGrid) {
            elements.shopProductsGrid.addEventListener('click', (event) => {
                const button = event.target.closest('.shop-product-btn');
                if (button) {
                    const productId = button.dataset.productId;
                    const action = button.dataset.action;
                    if (productId && action) {
                        handleProductPanelAction(productId, action);
                    }
                }
            });
        }

        // 【新增】商品弹窗的显示/隐藏逻辑
        if (elements.viewShopProductsBtn) {
            elements.viewShopProductsBtn.addEventListener('click', () => {
                if (elements.shopProductsModal) {
                    elements.shopProductsModal.style.display = 'block';
                }
            });
        }
        if (elements.closeShopProductsModalBtn) {
            elements.closeShopProductsModalBtn.addEventListener('click', () => {
                if (elements.shopProductsModal) {
                    elements.shopProductsModal.style.display = 'none';
                }
            });
        }
        if (elements.shopProductsModal) {
            elements.shopProductsModal.addEventListener('click', (e) => {
                if (e.target === elements.shopProductsModal) {
                    elements.shopProductsModal.style.display = 'none';
                }
            });
        }


        // 初始渲染店铺商品
        renderShopProducts();

        elements.autoEndInput.value = autoEndInterval || '';
        updateAutoEndStatus(elements.autoEndIndicator, elements.autoEndStatusText);

        // 客服名字输入框设置为只读
        if (elements.csNameInput) {
            elements.csNameInput.value = customerServiceName || '';
            elements.csNameInput.readOnly = true;
        }

        // 【新增】初始化会话转接功能
        initTransferFeature();

        // AI功能事件处理已删除





        // AI延迟设置已删除







        if(elements.openAITrainingDialogBtn) {
            elements.openAITrainingDialogBtn.addEventListener('click', openTrainingDialog);
        }
        if(elements.clearAIKnowledgeBtn) {
            elements.clearAIKnowledgeBtn.addEventListener('click', () => {
                if (window.confirm('您确定要清除所有AI智能体学习到的知识吗？\n此操作不可撤销，包括对话知识。')) {
                    aiAgentKnowledgeBase = { conversations: [], dynamic_rules: [] };
                    trainingDialogMessages = [];
                    saveData();
                    renderTrainingDialogContent();
                    showNotification(elements.notification, "AI训练内容已清除");
                    addStatusMonitorEvent('AI训练内容已清除', 'warning');
                }
            });
        }




        updateStats(elements.replyCount, elements.lastReplyTimeElement);
        updateStatus(elements.statusIndicator, elements.statusValue);

        elements.enableBtn.addEventListener('click', () => {
            isAutoReplyEnabled = true;
            GM_setValue('isAutoReplyEnabled_v9', true); // 更新到新键值
            updateStatus(elements.statusIndicator, elements.statusValue);
            showNotification(elements.notification, "自动回复已开启");
            addStatusMonitorEvent('自动回复已开启', 'success');
            saveData();
            startAutoReply();
        });

        elements.disableBtn.addEventListener('click', () => {
            isAutoReplyEnabled = false;
            GM_setValue('isAutoReplyEnabled_v9', false); // 更新到新键值
            updateStatus(elements.statusIndicator, elements.statusValue);
            showNotification(elements.notification, "自动回复已停止");
            addStatusMonitorEvent('自动回复已停止', 'info');
            saveData();
            stopAutoReply();
        });

        elements.fileInput.addEventListener('change', (e) =>
            handleFileUpload(e, null, null, elements.notification));

        // ========== 恢复：AI相关事件绑定 ==========
        if (elements.aiToggle && elements.aiApiKeyInput && elements.aiModelSelect) {
            elements.aiToggle.checked = aiEnabled; // 使用加载的数据来设置状态
            elements.aiModelSelect.value = aiModel;

            // 渲染已保存的API密钥列表
            renderApiKeysList();

            if (elements.deepThinkingToggle) {
                elements.deepThinkingToggle.checked = deepThinkingEnabled;
                elements.deepThinkingToggle.addEventListener('change', function() {
                    deepThinkingEnabled = this.checked;
                    GM_setValue('deepThinkingEnabled', deepThinkingEnabled);
                    showNotification(elements.notification, `深度思考(R1)功能已${deepThinkingEnabled ? '启用' : '禁用'}`);
                    addStatusMonitorEvent(`深度思考(R1)功能已${deepThinkingEnabled ? '启用' : '禁用'}`, 'info');
                    saveData();
                });
            }

            updateAIStatus();

            elements.aiToggle.addEventListener('change', function() {
                aiEnabled = this.checked;
                GM_setValue('aiEnabled_v9', aiEnabled); // 更新到新键值
                updateAIStatus();
                showNotification(elements.notification, `AI回复功能已${aiEnabled ? '启用' : '禁用'}`);
                addStatusMonitorEvent(`AI回复功能已${aiEnabled ? '启用' : '禁用'}`, 'info');
                saveData(); // 实时同步
            });

            // 【优化2】内置API密钥按钮事件
            if (elements.saveBuiltinApiKeysBtn && elements.builtinApiKeysInput) {
                // 初始化时显示当前内置密钥
                elements.builtinApiKeysInput.value = BUILTIN_API_KEYS;

                elements.saveBuiltinApiKeysBtn.addEventListener('click', async function() {
                    const builtinKeysText = elements.builtinApiKeysInput.value.trim();
                    if (!builtinKeysText) {
                        showNotification(elements.notification, "请输入内置API密钥");
                        return;
                    }

                    // 禁用按钮，显示处理中状态
                    const originalText = elements.saveBuiltinApiKeysBtn.textContent;
                    const originalDisabled = elements.saveBuiltinApiKeysBtn.disabled;
                    elements.saveBuiltinApiKeysBtn.disabled = true;
                    elements.saveBuiltinApiKeysBtn.textContent = '处理中...';
                    elements.saveBuiltinApiKeysBtn.style.opacity = '0.6';

                    try {
                        // 解析内置密钥
                        const lines = builtinKeysText.split('\n')
                            .map(line => line.trim())
                            .filter(line => line && !line.startsWith('请在这里') && !line.startsWith('sk-your-api-key-here'));

                        const validKeys = [];
                        const invalidLines = [];

                        for (const line of lines) {
                            if (line.startsWith('sk-') && line.length > 20) {
                                validKeys.push(line);
                            } else if (line.length > 0) {
                                invalidLines.push(line);
                            }
                        }

                        if (validKeys.length === 0) {
                            showNotification(elements.notification, "未找到有效的API密钥", 'error');
                            return;
                        }

                        // 添加有效密钥到列表
                        let addedCount = 0;
                        for (const key of validKeys) {
                            if (!aiApiKeys.includes(key)) {
                                aiApiKeys.push(key);
                                aiApiKeyStatus.push(true); // 内置密钥默认可用
                                addedCount++;
                            }
                        }

                        // 保存到存储
                        GM_setValue('aiApiKeys', JSON.stringify(aiApiKeys));
                        GM_setValue('aiApiKeyStatus', JSON.stringify(aiApiKeyStatus));

                        // 重新渲染列表
                        renderApiKeysList();
                        updateAIStatus();
                        saveData();

                        // 显示结果
                        if (addedCount > 0) {
                            showNotification(elements.notification, `成功添加 ${addedCount} 个内置API密钥`);
                            addStatusMonitorEvent(`成功添加 ${addedCount} 个内置API密钥`, 'success');
                        } else {
                            showNotification(elements.notification, "所有内置密钥都已存在");
                        }

                        if (invalidLines.length > 0) {
                            addStatusMonitorEvent(`跳过 ${invalidLines.length} 行无效内容`, 'warning');
                        }

                    } catch (error) {
                        console.error('处理内置API密钥时发生错误:', error);
                        showNotification(elements.notification, `处理失败: ${error.message}`, 'error');
                        addStatusMonitorEvent(`内置API密钥处理失败: ${error.message}`, 'error');
                    } finally {
                        // 恢复按钮状态
                        elements.saveBuiltinApiKeysBtn.disabled = originalDisabled;
                        elements.saveBuiltinApiKeysBtn.textContent = originalText;
                        elements.saveBuiltinApiKeysBtn.style.opacity = '';
                    }
                });
            }

            // 保存API密钥按钮事件
            elements.saveApiKeyBtn.addEventListener('click', async function() {
                const newApiKey = elements.aiApiKeyInput.value.trim();
                if (!newApiKey) {
                    showNotification(elements.notification, "请输入API密钥");
                    return;
                }

                // 检查是否已存在
                if (aiApiKeys.includes(newApiKey)) {
                    showNotification(elements.notification, "该API密钥已存在");
                    return;
                }

                // 禁用保存按钮，显示验证中状态
                const originalText = elements.saveApiKeyBtn.textContent;
                const originalDisabled = elements.saveApiKeyBtn.disabled;
                elements.saveApiKeyBtn.disabled = true;
                elements.saveApiKeyBtn.textContent = '验证中...';
                elements.saveApiKeyBtn.style.opacity = '0.6';

                try {
                    // 验证API密钥
                    showNotification(elements.notification, "正在验证API密钥...", 'info');
                    addStatusMonitorEvent('开始验证API密钥', 'info');

                    const validationResult = await validateApiKey(newApiKey);

                    if (validationResult.valid) {
                        // 验证成功，保存密钥
                        aiApiKeys.push(newApiKey);
                        aiApiKeyStatus.push(true); // 新密钥默认可用
                        GM_setValue('aiApiKeys', JSON.stringify(aiApiKeys));
                        GM_setValue('aiApiKeyStatus', JSON.stringify(aiApiKeyStatus));

                        // 清空输入框
                        elements.aiApiKeyInput.value = '';

                        // 重新渲染列表
                        renderApiKeysList();

                        showNotification(elements.notification, "API密钥验证成功并已保存");
                        addStatusMonitorEvent(`API密钥验证成功并已保存 (可用模型: ${validationResult.models?.length || 0}个)`, 'success');
                        saveData();
                    } else {
                        // 验证失败
                        showNotification(elements.notification, `API密钥验证失败: ${validationResult.error}`, 'error');
                        addStatusMonitorEvent(`API密钥验证失败: ${validationResult.error}`, 'error');
                        console.error('API密钥验证失败:', validationResult.error);
                    }
                } catch (error) {
                    console.error('API密钥验证过程中发生异常:', error);
                    showNotification(elements.notification, `验证过程中发生错误: ${error.message}`, 'error');
                    addStatusMonitorEvent(`API密钥验证异常: ${error.message}`, 'error');
                } finally {
                    // 恢复保存按钮状态
                    elements.saveApiKeyBtn.disabled = originalDisabled;
                    elements.saveApiKeyBtn.textContent = originalText;
                    elements.saveApiKeyBtn.style.opacity = '';
                }
            });

            elements.aiModelSelect.addEventListener('change', function() {
                aiModel = this.value;
                GM_setValue('aiModel', aiModel);
                showNotification(elements.notification, `模型已切换为: ${aiModel}`);
                addStatusMonitorEvent(`模型已切换为: ${aiModel}`, 'info');
                saveData();
            });
        }

        if (elements.aiReplyDelayInput) {
            elements.aiReplyDelayInput.value = aiReplyDelay || 0;
            elements.aiReplyDelayInput.addEventListener('change', function() {
                const delay = parseInt(this.value) || 0;
                setAIReplyDelay(delay);
            });
        }

        if (elements.saveAutoEndBtn) {
            elements.saveAutoEndBtn.addEventListener('click', () => {
                const seconds = parseInt(elements.autoEndInput.value);
                if (isNaN(seconds) || seconds <= 0) {
                    showNotification(elements.notification, "请输入一个大于0的有效秒数");
                    return;
                }
                autoEndInterval = seconds;
                GM_setValue('autoEndInterval', autoEndInterval);

                autoEndNextTriggerTimestamp = Date.now() + autoEndInterval * 1000;
                GM_setValue('autoEndNextTriggerTimestamp', autoEndNextTriggerTimestamp);

                setupAutoEndCountdown();
                updateAutoEndStatus(elements.autoEndIndicator, elements.autoEndStatusText);
                showNotification(elements.notification, `自动结束时间设置为${autoEndInterval}秒`);
                addStatusMonitorEvent(`自动结束时间设置为${autoEndInterval}秒`, 'info');
                saveData();
            });
        }

        if (elements.stopAutoEndBtn) {
            elements.stopAutoEndBtn.addEventListener('click', () => {
                autoEndInterval = 0;
                autoEndNextTriggerTimestamp = 0;
                GM_setValue('autoEndInterval', 0);
                GM_setValue('autoEndNextTriggerTimestamp', 0);

                if (autoEndCountdownIntervalId) clearInterval(autoEndCountdownIntervalId);

                updateAutoEndStatus(elements.autoEndIndicator, elements.autoEndStatusText);
                if (elements.autoEndCountdownDisplay) elements.autoEndCountdownDisplay.textContent = '下次检查倒计时: 已停止';

                showNotification(elements.notification, "自动结束功能已停止");
                addStatusMonitorEvent('自动结束功能已停止', 'info');
                saveData();
            });
        }

        if (elements.clearStatusBtn) {
            elements.clearStatusBtn.addEventListener('click', () => {
                statusMonitorEvents = [];
                updateStatusMonitorUI();
                addStatusMonitorEvent('状态日志已清空', 'info');
            });
        }

        if (elements.viewLogHistoryBtn) {
            elements.viewLogHistoryBtn.addEventListener('click', () => {
                renderHistoricalLogs();
                elements.logHistoryModal.style.display = 'block';
            });
        }
        if (elements.closeLogHistoryModalBtn) {
            elements.closeLogHistoryModalBtn.addEventListener('click', () => {
                elements.logHistoryModal.style.display = 'none';
            });
        }
        if (elements.logHistoryModal) {
            elements.logHistoryModal.addEventListener('click', (e) => {
                if (e.target === elements.logHistoryModal) {
                    elements.logHistoryModal.style.display = 'none';
                }
            });
        }

        if (elements.feedbackRegistryCount) {
            elements.feedbackRegistryCount.textContent = feedbackRegistry.size;
        }
        if (elements.downloadFeedbackBtn) {
            elements.downloadFeedbackBtn.addEventListener('click', downloadFeedbackReport);
        }
        if (elements.uploadFeedbackBtn) {
            elements.uploadFeedbackBtn.addEventListener('click', () => elements.feedbackFileInput.click());
        }
        if (elements.feedbackFileInput) {
            elements.feedbackFileInput.addEventListener('change', (e) => handleFeedbackUpload(e));
        }
        if (elements.replyToFeedbackUsersBtn) {
            elements.replyToFeedbackUsersBtn.addEventListener('click', replyToRegisteredUsers);
        }





        if (isAutoReplyEnabled) {
            startAutoReply();
        }

        updateStatusMonitorUI();

        setupAutoEndCountdown();

    }, 'UI组件初始化');

    // 更新自动结束状态的UI显示
    const updateAutoEndStatus = withErrorHandling(function(indicator, statusText) {
        if (!indicator || !statusText) return;

        const canRun = autoEndInterval > 0 && window.location.href.includes('/shop/kf');

        if (canRun) {
            indicator.className = 'refresh-indicator active';
            statusText.textContent = `自动结束已启用 (${autoEndInterval}秒)`;
        } else {
            indicator.className = 'refresh-indicator';
            if (autoEndInterval > 0) {
                statusText.textContent = `已暂停 (非客服页)`;
            } else {
                statusText.textContent = '未启用自动结束';
            }
        }
    }, '自动结束状态更新');



    /**
     * @description 【功能修复】触发扫描后刷新页面的核心函数
     * @param {string} reason - 触发刷新的原因，用于日志记录
     */
    const triggerPostScanRefresh = withErrorHandling(function(reason) {
        // 只有在客服页面且总开关开启时才执行
        if (!window.location.href.includes('/shop/kf') || !isAutoReplyEnabled) {
            return;
        }
        // 检查是否有任务正在进行，如果有则不刷新
        if(processingSession || isAutoChecking || isAutoEnding || isReplyingToFeedback) {
            addStatusMonitorEvent(`【刷新阻断】因有任务正在运行，已阻止本次刷新。`, 'warning');
            return;
        }

        addStatusMonitorEvent(`【自动刷新】扫描周期结束 (${reason})，将在2秒后刷新页面。`, 'success');
        setTimeout(() => {
            saveData(); // 刷新前保存所有最新状态
            location.reload();
        }, 2000); // 2秒后刷新
    }, '触发扫描后刷新');

    // 保存数据
    const saveData = withErrorHandling(function() {
        // 此处保存所有需要跨页面、跨会话保留的数据
        GM_setValue('todayReplies', todayReplies);
        GM_setValue('lastReplyTime', lastReplyTime);
        GM_setValue('isAutoReplyEnabled_v9', isAutoReplyEnabled);
        GM_setValue('lastSaveDay', new Date().toDateString());
        GM_setValue('customerServiceName', customerServiceName);
        GM_setValue('autoEndInterval', autoEndInterval);
        GM_setValue('autoEndNextTriggerTimestamp', autoEndNextTriggerTimestamp);
        // ========== 恢复：AI相关存储 ==========
        GM_setValue('aiEnabled_v9', aiEnabled);
        GM_setValue('aiApiKeys', JSON.stringify(aiApiKeys));
        GM_setValue('aiApiKeyStatus', JSON.stringify(aiApiKeyStatus));
        GM_setValue('currentApiKeyIndex', currentApiKeyIndex);
        GM_setValue('aiModel', aiModel);
        GM_setValue('deepThinkingEnabled', deepThinkingEnabled);
        GM_setValue('aiReplyDelay', aiReplyDelay);

        GM_setValue('sessionReplyStatus', JSON.stringify(Object.fromEntries(sessionReplyStatus)));
        GM_setValue('groupStates', groupStates);
        GM_setValue('aiAgentKnowledgeBase', aiAgentKnowledgeBase);
        GM_setValue('trainingDialogMessages', trainingDialogMessages);
        GM_setValue('feedbackRegistry', JSON.stringify(Array.from(feedbackRegistry.entries())));
        // ========== 恢复：保存AI会话上下文 ==========
        GM_setValue('conversationContexts', JSON.stringify(Object.fromEntries(conversationContexts)));
        GM_setValue('historicalLogs', JSON.stringify(historicalLogs));
        // 【新增】保存店铺商品数据
        GM_setValue('shopProducts', shopProducts);
        GM_setValue('shopProductDescriptions', JSON.stringify(Object.fromEntries(shopProductDescriptions)));
        // 【新增】保存会话转接设置
        GM_setValue('transferKeywords', transferKeywords);
        GM_setValue('transferToAgent', transferToAgent);
        GM_setValue('availableAgents', JSON.stringify(availableAgents));

    }, '数据保存');



    function updateStats(replyCount, lastReplyTimeElement) {
        if (replyCount) replyCount.textContent = todayReplies;
        if (lastReplyTimeElement) lastReplyTimeElement.textContent = lastReplyTime;
    }

    function updateStatus(statusIndicator, statusValue) {
        if (!statusIndicator || !statusValue) return;

        if (isAutoReplyEnabled) {
            statusIndicator.className = 'status-indicator status-on';
            statusValue.textContent = '运行中';
        } else {
            statusIndicator.className = 'status-indicator status-off';
            statusValue.textContent = '已停止';
        }
    }

    // ========== 恢复：AI状态更新函数 ==========
    function updateAIStatus() {
        const indicator = document.getElementById('aiStatusIndicator');
        const statusText = document.getElementById('aiStatusText');
        const aiReplyStatusEl = document.getElementById('aiReplyStatus');

        if (indicator && statusText) {
            indicator.className = 'ai-status-indicator';
            switch (aiStatus) {
                case 'active':
                    indicator.classList.add('active');
                    statusText.textContent = '小梅花AI客服正在回复消息';
                    break;
                case 'error':
                    statusText.textContent = 'API密钥未设置';
                    break;
                default:
                    statusText.textContent = aiEnabled ? 'AI已启用' : 'AI已禁用';
                    if (aiEnabled && aiApiKeys.length > 0) indicator.classList.add('active');
            }
        }

        if (aiReplyStatusEl) {
            if (aiThinking) {
                aiReplyStatusEl.textContent = '小梅花AI客服正在回复消息';
                aiReplyStatusEl.style.color = '#E6A23C'; // 橙色
            } else if (aiEnabled && aiApiKeys.length > 0) {
                aiReplyStatusEl.textContent = '小梅花AI客服已启用';
                aiReplyStatusEl.style.color = '#07C160'; // 绿色
            } else {
                aiReplyStatusEl.textContent = '小梅花AI客服未启用';
                aiReplyStatusEl.style.color = '#FA5151'; // 红色
            }
        }
    }

    // ========== 新增：API密钥管理函数 ==========
    const renderApiKeysList = withErrorHandling(function() {
        const container = document.getElementById('apiKeysStatus');
        if (!container) return;

        if (aiApiKeys.length === 0) {
            container.innerHTML = '<div class="api-keys-status-display">暂无保存的API密钥</div>';
            return;
        }

        const availableCount = aiApiKeyStatus.filter(status => status).length;
        const totalCount = aiApiKeys.length;
        const currentKey = aiApiKeys[currentApiKeyIndex];
        const currentStatus = aiApiKeyStatus[currentApiKeyIndex] ? '正常' : '异常';

        container.innerHTML = `
            <div class="api-keys-status-display" id="apiKeysStatusBtn">
                已保存 ${totalCount} 个密钥 (可用: ${availableCount}) | 当前状态: ${currentStatus} | 点击查看全部密钥
            </div>
        `;

        // 延迟添加点击事件监听器，确保DOM元素已创建
        setTimeout(() => {
            const statusBtn = document.getElementById('apiKeysStatusBtn');
            console.log('查找状态按钮:', statusBtn);
            if (statusBtn) {
                statusBtn.addEventListener('click', function() {
                    console.log('状态按钮被点击');
                    openApiKeysModal();
                });
                console.log('状态按钮事件监听器已添加');
            } else {
                console.error('未找到状态按钮元素');
            }
        }, 50);
    }, '渲染API密钥状态');

    // 打开API密钥管理弹窗
    const openApiKeysModal = withErrorHandling(function() {
        console.log('openApiKeysModal 被调用');
        const modal = document.getElementById('apiKeysModal');
        console.log('找到弹窗元素:', modal);
        if (modal) {
            // 确保弹窗事件监听器已初始化
            initApiKeysModal();
            modal.style.display = 'block';
            renderApiKeysModal();
            addStatusMonitorEvent('API密钥管理弹窗已打开', 'info');
            console.log('API密钥弹窗已打开并初始化');
        } else {
            console.error('未找到API密钥弹窗元素');
            addStatusMonitorEvent('无法打开API密钥弹窗：元素未找到', 'error');
        }
    }, '打开API密钥弹窗');

    // 将函数暴露到全局作用域
    window.openApiKeysModal = openApiKeysModal;

    // 关闭API密钥管理弹窗
    const closeApiKeysModal = withErrorHandling(function() {
        const modal = document.getElementById('apiKeysModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }, '关闭API密钥弹窗');

    // 渲染弹窗中的API密钥列表
    const renderApiKeysModal = withErrorHandling(function() {
        const container = document.getElementById('apiKeysModalList');
        if (!container) return;

        if (aiApiKeys.length === 0) {
            container.innerHTML = '<div class="empty-api-keys">暂无保存的API密钥</div>';
            return;
        }

        container.innerHTML = aiApiKeys.map((key, index) => {
            const maskedKey = key.substring(0, 8) + '...' + key.substring(key.length - 8);
            const isCurrentKey = index === currentApiKeyIndex;
            const isAvailable = aiApiKeyStatus[index];

            let statusClass, statusText;
            if (isCurrentKey) {
                statusClass = 'current';
                statusText = '当前使用';
            } else if (isAvailable) {
                statusClass = 'available';
                statusText = '可用';
            } else {
                statusClass = 'disabled';
                statusText = '异常';
            }

            return `
                <div class="api-key-modal-item ${!isAvailable ? 'disabled' : ''}" data-index="${index}">
                    <div class="api-key-modal-display">${maskedKey}</div>
                    <div class="api-key-modal-actions">
                        <span class="api-key-modal-status ${statusClass}">${statusText}</span>
                        <button type="button" class="btn-modal-validate-api" data-index="${index}">验证</button>
                        <button type="button" class="btn-modal-delete-api" data-index="${index}">删除</button>
                    </div>
                </div>
            `;
        }).join('');
    }, '渲染弹窗API密钥列表');

    // 从弹窗删除API密钥
    const deleteApiKeyFromModal = withErrorHandling(function(index) {
        console.log('deleteApiKeyFromModal 被调用，索引:', index);
        console.log('当前API密钥数组:', aiApiKeys);
        console.log('当前API密钥状态:', aiApiKeyStatus);

        if (index < 0 || index >= aiApiKeys.length) {
            console.error('无效的索引:', index);
            addStatusMonitorEvent(`删除失败：无效的索引 ${index}`, 'error');
            return;
        }

        const deletedKey = aiApiKeys[index].substring(0, 8) + '...';
        console.log('准备删除密钥:', deletedKey);

        // 如果删除的是当前使用的密钥，切换到第一个可用的密钥
        if (index === currentApiKeyIndex) {
            console.log('删除的是当前使用的密钥，需要切换');
            // 寻找下一个可用的密钥
            let nextIndex = -1;
            for (let i = 0; i < aiApiKeys.length; i++) {
                if (i !== index && aiApiKeyStatus[i]) {
                    nextIndex = i;
                    break;
                }
            }
            currentApiKeyIndex = nextIndex >= 0 ? (nextIndex > index ? nextIndex - 1 : nextIndex) : 0;
            if (aiApiKeys.length === 1) {
                currentApiKeyIndex = -1; // 没有密钥了
            }
            console.log('新的当前密钥索引:', currentApiKeyIndex);
        } else if (index < currentApiKeyIndex) {
            currentApiKeyIndex--; // 调整当前索引
            console.log('调整当前密钥索引为:', currentApiKeyIndex);
        }

        // 立即删除，无需确认
        aiApiKeys.splice(index, 1);
        aiApiKeyStatus.splice(index, 1);

        console.log('删除后的API密钥数组:', aiApiKeys);
        console.log('删除后的API密钥状态:', aiApiKeyStatus);

        // 保存到存储
        GM_setValue('aiApiKeys', JSON.stringify(aiApiKeys));
        GM_setValue('aiApiKeyStatus', JSON.stringify(aiApiKeyStatus));
        GM_setValue('currentApiKeyIndex', currentApiKeyIndex);

        // 更新界面
        renderApiKeysModal();
        renderApiKeysList();
        updateAIStatus();

        // 显示成功消息
        const notification = document.getElementById('notification');
        if (notification) {
            showNotification(notification, `API密钥 ${deletedKey} 已删除`);
        }
        addStatusMonitorEvent(`API密钥 ${deletedKey} 已删除`, 'success');
        saveData();

        console.log('API密钥删除完成');
    }, '从弹窗删除API密钥');

    // 将函数暴露到全局作用域（兼容性）
    window.deleteApiKeyFromModal = deleteApiKeyFromModal;

    // 从弹窗验证API密钥
    const validateApiKeyFromModal = withErrorHandling(async function(index) {
        console.log('validateApiKeyFromModal 被调用，索引:', index);

        if (index < 0 || index >= aiApiKeys.length) {
            console.error('无效的索引:', index);
            addStatusMonitorEvent(`验证失败：无效的索引 ${index}`, 'error');
            return;
        }

        const apiKey = aiApiKeys[index];
        const maskedKey = apiKey.substring(0, 8) + '...';

        // 找到对应的验证按钮并禁用
        const validateBtn = document.querySelector(`[data-index="${index}"].btn-modal-validate-api`);
        if (validateBtn) {
            validateBtn.disabled = true;
            validateBtn.textContent = '验证中...';
        }

        try {
            console.log('开始验证API密钥:', maskedKey);
            addStatusMonitorEvent(`开始验证API密钥 ${maskedKey}`, 'info');

            const validationResult = await validateApiKey(apiKey);

            if (validationResult.valid) {
                // 验证成功，更新状态
                aiApiKeyStatus[index] = true;
                GM_setValue('aiApiKeyStatus', JSON.stringify(aiApiKeyStatus));

                console.log(`API密钥 ${maskedKey} 验证成功`);
                addStatusMonitorEvent(`API密钥 ${maskedKey} 验证成功 (可用模型: ${validationResult.models?.length || 0}个)`, 'success');

                // 重新渲染弹窗和列表
                renderApiKeysModal();
                renderApiKeysList();
                updateAIStatus();
                saveData();
            } else {
                // 验证失败，更新状态
                aiApiKeyStatus[index] = false;
                GM_setValue('aiApiKeyStatus', JSON.stringify(aiApiKeyStatus));

                console.error(`API密钥 ${maskedKey} 验证失败:`, validationResult.error);
                addStatusMonitorEvent(`API密钥 ${maskedKey} 验证失败: ${validationResult.error}`, 'error');

                // 重新渲染弹窗和列表
                renderApiKeysModal();
                renderApiKeysList();
                updateAIStatus();
                saveData();
            }
        } catch (error) {
            console.error('API密钥验证过程中发生异常:', error);
            addStatusMonitorEvent(`API密钥 ${maskedKey} 验证异常: ${error.message}`, 'error');

            // 验证异常时标记为不可用
            aiApiKeyStatus[index] = false;
            GM_setValue('aiApiKeyStatus', JSON.stringify(aiApiKeyStatus));

            // 重新渲染弹窗和列表
            renderApiKeysModal();
            renderApiKeysList();
            updateAIStatus();
            saveData();
        } finally {
            // 恢复按钮状态（如果按钮还存在的话）
            if (validateBtn) {
                validateBtn.disabled = false;
                validateBtn.textContent = '验证';
            }
        }
    }, '从弹窗验证API密钥');

    // 将函数暴露到全局作用域（兼容性）
    window.validateApiKeyFromModal = validateApiKeyFromModal;

    // 验证API密钥是否有效
    const validateApiKey = withErrorHandling(async function(apiKey) {
        console.log('开始验证API密钥:', apiKey.substring(0, 8) + '...');

        if (!apiKey || !apiKey.startsWith('sk-')) {
            return {
                valid: false,
                error: 'API密钥格式不正确，应以sk-开头'
            };
        }

        try {
            // 创建AbortController用于超时控制
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

            // 使用DeepSeek API进行验证
            const response = await fetch('https://api.deepseek.com/v1/models', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            console.log('API验证响应状态:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('API验证成功，可用模型数量:', data.data?.length || 0);
                return {
                    valid: true,
                    models: data.data || [],
                    message: 'API密钥验证成功'
                };
            } else if (response.status === 401) {
                return {
                    valid: false,
                    error: 'API密钥无效或已过期'
                };
            } else if (response.status === 429) {
                return {
                    valid: false,
                    error: 'API请求频率过高，请稍后再试'
                };
            } else {
                const errorText = await response.text();
                console.error('API验证失败:', response.status, errorText);
                return {
                    valid: false,
                    error: `API验证失败 (${response.status}): ${errorText}`
                };
            }
        } catch (error) {
            console.error('API密钥验证异常:', error);
            if (error.name === 'AbortError') {
                return {
                    valid: false,
                    error: '验证超时，请检查网络连接'
                };
            } else if (error.message.includes('fetch')) {
                return {
                    valid: false,
                    error: '网络连接失败，请检查网络设置'
                };
            } else {
                return {
                    valid: false,
                    error: `验证过程中发生错误: ${error.message}`
                };
            }
        }
    }, 'API密钥验证');

    // 初始化API密钥弹窗
    let apiKeysModalInitialized = false;
    const initApiKeysModal = withErrorHandling(function() {
        if (apiKeysModalInitialized) {
            console.log('API密钥弹窗已经初始化过了');
            return;
        }

        const modal = document.getElementById('apiKeysModal');
        const closeBtn = document.getElementById('closeApiKeysModal');

        if (closeBtn) {
            closeBtn.addEventListener('click', closeApiKeysModal);
        }

        if (modal) {
            // 点击弹窗外部关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeApiKeysModal();
                }
            });

            // 使用事件委托处理按钮点击
            modal.addEventListener('click', function(e) {
                const index = parseInt(e.target.getAttribute('data-index'));

                if (e.target && e.target.classList.contains('btn-modal-delete-api')) {
                    console.log('删除按钮被点击，索引:', index);
                    if (!isNaN(index)) {
                        deleteApiKeyFromModal(index);
                    }
                } else if (e.target && e.target.classList.contains('btn-modal-validate-api')) {
                    console.log('验证按钮被点击，索引:', index);
                    if (!isNaN(index)) {
                        validateApiKeyFromModal(index);
                    }
                }
            });

            apiKeysModalInitialized = true;
            console.log('API密钥弹窗初始化完成');
        }
    }, '初始化API密钥弹窗');

    // 获取当前API密钥
    const getCurrentApiKey = withErrorHandling(function() {
        if (aiApiKeys.length === 0) return null;
        if (currentApiKeyIndex < 0 || currentApiKeyIndex >= aiApiKeys.length) {
            currentApiKeyIndex = 0;
        }
        return aiApiKeys[currentApiKeyIndex];
    }, '获取当前API密钥');

    // 标记API密钥为不可用
    const markApiKeyAsDisabled = withErrorHandling(function(index) {
        if (index >= 0 && index < aiApiKeyStatus.length) {
            aiApiKeyStatus[index] = false;
            GM_setValue('aiApiKeyStatus', JSON.stringify(aiApiKeyStatus));
            addStatusMonitorEvent(`API密钥 ${index + 1} 已标记为不可用`, 'warning');
            renderApiKeysList();
        }
    }, '标记API密钥不可用');

    // 切换到下一个可用的API密钥
    const switchToNextAvailableApiKey = withErrorHandling(function() {
        if (aiApiKeys.length <= 1) return false;

        // 标记当前密钥为不可用
        if (currentApiKeyIndex >= 0 && currentApiKeyIndex < aiApiKeyStatus.length) {
            markApiKeyAsDisabled(currentApiKeyIndex);
        }

        // 寻找下一个可用的密钥
        let nextIndex = -1;
        for (let i = 0; i < aiApiKeys.length; i++) {
            const checkIndex = (currentApiKeyIndex + 1 + i) % aiApiKeys.length;
            if (aiApiKeyStatus[checkIndex]) {
                nextIndex = checkIndex;
                break;
            }
        }

        if (nextIndex === -1) {
            addStatusMonitorEvent('所有API密钥都不可用，请检查密钥状态', 'error');
            return false;
        }

        currentApiKeyIndex = nextIndex;
        GM_setValue('currentApiKeyIndex', currentApiKeyIndex);

        addStatusMonitorEvent(`已切换到可用API密钥 (${currentApiKeyIndex + 1}/${aiApiKeys.length})`, 'info');
        renderApiKeysList();
        return true;
    }, '切换到可用API密钥');

    // ========== 恢复：AI延迟设置函数 ==========
    const setAIReplyDelay = withErrorHandling(function(delay) {
        aiReplyDelay = parseInt(delay) || 0;
        GM_setValue('aiReplyDelay', aiReplyDelay);
        showNotification(document.getElementById('notification'), `AI回复延迟设置为${aiReplyDelay}秒`);
        addStatusMonitorEvent(`AI回复延迟设置为${aiReplyDelay}秒`, 'info');
        saveData();
    }, '设置AI延迟');

    // ========== 恢复：AI回复生成函数 ==========
    const generateAIResponse = withErrorHandling(async function(message, sessionId, customSystemPrompt = null, sessionName = '用户') {
        if (!aiEnabled || aiApiKeys.length === 0) {
            return null;
        }

        aiStatus = 'active';
        aiThinking = true;
        updateAIStatus();
        addStatusMonitorEvent(`小梅花AI客服正在回复消息`, 'info');

        // 智能重试机制
        let maxRetries = Math.min(3, aiApiKeys.length); // 最多重试3次或所有密钥数量
        let lastError = null;

        for (let attempt = 0; attempt < maxRetries; attempt++) {
            try {
                // 获取当前使用的API密钥
                const currentApiKey = getCurrentApiKey();
                if (!currentApiKey) {
                    throw new Error('没有可用的API密钥');
                }

                // 检查当前密钥是否可用
                if (!aiApiKeyStatus[currentApiKeyIndex]) {
                    throw new Error('当前API密钥已被标记为不可用');
                }

                // 准备上下文和知识库
                let context = (sessionId && conversationContexts.has(sessionId)) ? conversationContexts.get(sessionId) : [];
                context.push({
                    role: 'user',
                    content: message
                });

                let knowledgeBasePrompt = '';
                if (aiAgentKnowledgeBase.conversations.length > 0) {
                    knowledgeBasePrompt += '[智能体训练-对话知识]:\n' +
                        aiAgentKnowledgeBase.conversations.map(c => {
                            if (c.question.includes('关于文件') && c.question.includes('的内容')) {
                                return `文件知识:"${c.answer}"`;
                            } else {
                                return `用户问:"${c.question}", 你需要这样回答:"${c.answer}"`;
                            }
                        }).join('\n') + '\n';
                }

                // 店铺商品知识
                if (shopProducts.length > 0) {
                    const productsWithDesc = shopProducts.filter(p => shopProductDescriptions.get(p.id)?.trim());
                    if (productsWithDesc.length > 0) {
                        knowledgeBasePrompt += '[店铺商品知识]:\n' + productsWithDesc.map(p =>
                            `商品"${p.title}"(ID:${p.id},价格:${p.price})的介绍:"${shopProductDescriptions.get(p.id)}"`
                        ).join('\n') + '\n';
                    }
                }

                const systemPrompt = (customSystemPrompt || aiSystemPrompt) + `\n\n[背景知识库]:\n${knowledgeBasePrompt}`;
                let finalSystemPrompt = systemPrompt;
                if (deepThinkingEnabled && aiModel === 'deepseek-reasoner') {
                    finalSystemPrompt += "\n\n[深度思考模式已启用] 请基于深度思考(R1)功能对问题进行深入分析和全面回答，确保回复内容详实且有深度。";
                }

                // 发起API请求
                const response = await fetch('https://api.deepseek.com/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentApiKey}`
                    },
                    body: JSON.stringify({
                        model: aiModel,
                        messages: [{
                            role: 'system',
                            content: finalSystemPrompt
                        }, ...context],
                        max_tokens: 4000,
                        temperature: 0.7,
                        stream: false
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`API请求失败 (${response.status}): ${errorData.error?.message || response.statusText}`);
                }

                const data = await response.json();
                let aiReply = data.choices[0]?.message?.content?.trim() || '';

                if (!aiReply) {
                    throw new Error('AI返回空回复');
                }

                // 保存对话上下文
                if (sessionId) {
                    context.push({
                        role: 'assistant',
                        content: aiReply
                    });
                    if (context.length > 10) {
                        context = context.slice(-10);
                    }
                    conversationContexts.set(sessionId, context);
                }

                addStatusMonitorEvent(`小梅花AI客服成功回复"${sessionName}"`, 'success');
                aiStatus = 'inactive';
                aiThinking = false;
                updateAIStatus();
                return aiReply;

            } catch (error) {
                lastError = error;
                addStatusMonitorEvent(`API密钥 ${currentApiKeyIndex + 1} 调用失败: ${error.message}`, 'warning');

                // 如果不是最后一次尝试，切换到下一个可用密钥
                if (attempt < maxRetries - 1) {
                    const switched = switchToNextAvailableApiKey();
                    if (!switched) {
                        break; // 没有更多可用密钥
                    }
                    addStatusMonitorEvent(`正在尝试备用API密钥...`, 'info');
                    continue; // 继续下一次尝试
                }
            }
        }

        // 所有重试都失败了
        SecureLogger.error(`AI生成失败，已尝试 ${maxRetries} 次: ${lastError?.message}`);
        aiStatus = 'error';
        aiThinking = false;
        updateAIStatus();
        addStatusMonitorEvent(`AI回复失败，所有密钥都不可用: ${lastError?.message}`, 'error');
        return null;
    }, 'AI回复生成');

    function showNotification(notification, message, type = 'success') {
        if (!notification) return;

        notification.textContent = message;
        notification.style.display = 'block';

        // 根据类型设置不同的样式
        notification.className = 'notification'; // 重置类名
        switch (type) {
            case 'error':
                notification.classList.add('notification-error');
                notification.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)';
                notification.style.color = 'white';
                break;
            case 'info':
                notification.classList.add('notification-info');
                notification.style.background = 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)';
                notification.style.color = 'white';
                break;
            case 'warning':
                notification.classList.add('notification-warning');
                notification.style.background = 'linear-gradient(135deg, #feca57 0%, #ff9ff3 100%)';
                notification.style.color = '#333';
                break;
            case 'success':
            default:
                notification.classList.add('notification-success');
                notification.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                notification.style.color = 'white';
                break;
        }

        // 根据类型设置不同的显示时长
        const duration = type === 'error' ? 5000 : 3000;
        setTimeout(() => {
            notification.style.display = 'none';
        }, duration);
    }

    const handleFileUpload = withErrorHandling(function(e, templateList, ruleCount, notification) {
        if (!e.target.files || e.target.files.length === 0) return;

        const file = e.target.files[0];
        if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
            parseExcelFile(file, templateList, ruleCount, notification);
        } else {
            showNotification(notification, "请上传Excel文件 (.xlsx 或 .xls)");
            addStatusMonitorEvent('请上传Excel文件 (.xlsx 或 .xls)', 'warning');
        }

        e.target.value = '';
    }, '文件上传处理');





    const getSessionName = withErrorHandling(function(sessionItem) {
        // 优先从当前打开的聊天窗口标题获取，最准确
        const chatTitleName = document.querySelector('.chat-title .name, .chat-title .left .chat-customer-name');
        if (chatTitleName && chatTitleName.offsetParent !== null) { // 检查元素是否可见
            return chatTitleName.textContent.trim().substring(0, 20);
        }

        // 如果不在聊天窗口内，则从列表项中获取
        const nameSelectors = [
            '.name', '.nickname', '.session-name', '.user-name', '.customer-name', '[class*="name"]'
        ];

        for (const selector of nameSelectors) {
            const element = sessionItem.querySelector(selector);
            if (element && element.textContent) {
                return element.textContent.trim().substring(0, 20);
            }
        }
        return null;
    }, '会话名称获取');


    // 自动回复核心功能 ================================================
    const startAutoReply = withErrorHandling(function() {
        if (!isAutoReplyEnabled) return;

        // 优先启动实时监控模式
        startUnreadMonitoring();
        updateRealtimeMonitorStatus();


        setupAutoEndCountdown();

        lastMessageCount = 0;
        lastMessageSnapshot = '';
        addStatusMonitorEvent('自动回复已启动 - 实时监控模式', 'success');
    }, '自动回复启动');

    const stopAutoReply = withErrorHandling(function() {
        // 停止实时监控
        stopUnreadMonitoring();
        updateRealtimeMonitorStatus();

        if (autoCheckTimer) {
            clearInterval(autoCheckTimer);
            autoCheckTimer = null;
        }
        if (autoEndCountdownIntervalId) {
            clearInterval(autoEndCountdownIntervalId);
            autoEndCountdownIntervalId = null;
        }
        if (sessionPollingTimer) {
            clearInterval(sessionPollingTimer);
            sessionPollingTimer = null;
        }
        if (messageCheckTimer) {
            clearInterval(messageCheckTimer);
            messageCheckTimer = null;
        }

        isAutoChecking = false;
        isAutoEnding = false;
        isReplyingToFeedback = false;
        processingSession = false;
        currentSessionId = null;
        lastMessageCount = 0;
        lastMessageSnapshot = '';

        monitoredSessions.clear();


        const countdownDisplay = document.getElementById('autoEndCountdownDisplay');
        if (countdownDisplay) countdownDisplay.textContent = '下次检查倒计时: 已停止';

        if (aiScanTimer) {
            clearInterval(aiScanTimer);
            aiScanTimer = null;
        }

        addStatusMonitorEvent('自动回复已停止 - 实时监控已关闭', 'info');
    }, '自动回复停止');


    const checkIfSessionNeedsReply = withErrorHandling(function(sessionItem) {
        return hasUnrepliedMessages(sessionItem);
    }, '会话回复需求检查');

    const getSessionId = withErrorHandling(function(sessionItem) {
        if (!sessionItem) return `session_null_${Date.now()}`;
        return sessionItem.getAttribute('data-room-id') ||
            sessionItem.getAttribute('data-key') ||
            sessionItem.getAttribute('data-session-id') ||
            sessionItem.id ||
            `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }, '会话ID获取');

    const extractMessageContent = withErrorHandling(function(messageElement) {
        if (!messageElement) return { content: "", type: "unknown", element: null };

        const imageSelectors = ['.msg-image img', '.image', '.img', 'img:not(.msg-avatar)', '.image-message', '[class*="image"]', '[class*="img"]', '[class*="pic"]'];
        for(const selector of imageSelectors){
            const imgElement = messageElement.querySelector(selector);
            if (imgElement && !imgElement.classList.contains('msg-avatar')) {
                return {
                    content: imgElement.src || "IMAGE_MESSAGE_PLACEHOLDER",
                    type: "image",
                    element: imgElement
                };
            }
        }

        const emojiSelectors = ['.emoji', '.sticker', '.face', '.bq', '.expression', '[class*="emoji"]', '[class*="sticker"]', 'img.msg-avatar + img.msg-avatar'];
        if (emojiSelectors.some(selector => messageElement.querySelector(selector))) {
            return {
                content: "EMOJI_MESSAGE",
                type: "emoji",
                element: null
            };
        }

        const voiceSelectors = ['.voice', '.audio', '.voice-message', '[class*="voice"]', '[class*="audio"]'];
        if (voiceSelectors.some(selector => messageElement.querySelector(selector))) {
            return { content: "VOICE_MESSAGE", type: "audio", element: null };
        }

        const contentSelectors = [
            '.msg-text', '.message-content .text', '.bg-user', '.message-content',
            '.msg-content', '.text-content', '.chat-text',
            '[class*="content"] .selectable-text', '[class*="content"]'
        ];

        for (const selector of contentSelectors) {
            const element = messageElement.querySelector(selector);
            if (element) {
                let content = (element.innerText || element.textContent || "").trim();
                const timeElement = element.querySelector('.message-time, .msg-time');
                if (timeElement) {
                    content = content.replace(timeElement.textContent, '').trim();
                }
                if (content) {
                    return { content: content, type: "text", element: element };
                }
            }
        }

        let mainText = (messageElement.innerText || messageElement.textContent || "").trim();
        const timePatterns = /\d{1,2}:\d{2}(:\d{2})?(\s*(AM|PM))?/gi;
        let content = mainText.replace(timePatterns, "").trim();
        return { content: content, type: "text", element: messageElement };

    }, '消息内容与类型提取');

    const AI_PREFIX = "【AI消息回复】";

    function classifyMessage(messageObject) {
      if (!messageObject || !messageObject.type) {
        SecureLogger.warn("无效的消息对象格式，已默认为用户消息。");
        return "用户消息";
      }

      switch (messageObject.type) {
        case 'image':
          return "用户消息";
        case 'text':
          const content = messageObject.content || "";
          if (content.startsWith(AI_PREFIX)) {
            return "AI消息";
          } else {
            return "用户消息";
          }
        default:
          return "用户消息";
      }
    }

    const extractVoiceText = withErrorHandling(function(voiceElement) {
        if (!voiceElement) return null;

        const voiceTextSelectors = [
            '.voice-to-text', '.voice-text', '.text-content',
            '[class*="voice-text"]', '[class*="text-result"]'
        ];

        let current = voiceElement;
        while (current && current !== document.body) {
            for (const selector of voiceTextSelectors) {
                const element = current.querySelector(selector);
                if (element && element.textContent) {
                    return element.textContent.trim();
                }
            }
            if (current.parentElement === document.body) break;
            current = current.parentElement;
        }
        return null;
    }, '语音转文字');

    const isServiceMessage = withErrorHandling(function(messageElement) {
        if (!messageElement) return false;
        const messageData = extractMessageContent(messageElement);
        return classifyMessage(messageData) === 'AI消息';
    }, '客服消息检查');


    const isSystemReply = withErrorHandling(function(content) {
        if (!content) return false;

        const SYSTEM_REPLY_KEYWORDS = ["自动回复", "系统回复", "auto reply", "system reply", "您好，欢迎光临！"];
        const normalizedContent = content.toLowerCase().replace(/\s+/g, '');

        return SYSTEM_REPLY_KEYWORDS.some(keyword =>
            normalizedContent.includes(keyword.toLowerCase().replace(/\s+/g, ''))
        );
    }, '系统回复检查');

    const isCustomerMessage = withErrorHandling(function(messageElement) {
        if (!messageElement) return false;
        const messageData = extractMessageContent(messageElement);
        const classification = classifyMessage(messageData);
        return classification === '用户消息';
    }, '用户消息检查');

    const generateMessageId = withErrorHandling(function(messageElement, content, index) {
        const preExistingId = messageElement.id ||
            messageElement.getAttribute('data-msg-id') ||
            messageElement.getAttribute('data-key') ||
            messageElement.getAttribute('data-time');
        if (preExistingId) return `attr_id_${preExistingId}`;

        let hash = 0;
        const contentToHash = (typeof content === 'string' && content) ? content : "";
        for (let i = 0; i < contentToHash.length; i++) {
            const char = contentToHash.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash |= 0;
        }

        const timestamp = Date.now();
        return `idx_${index}_hash_${hash}_ts_${timestamp}`;
    }, '消息ID生成');



    const findBestAgentKnowledgeReply = withErrorHandling(function(normalizedMsg) {
        let bestMatch = { score: 0, reply: null };

        for (const convo of aiAgentKnowledgeBase.conversations) {
            const keywords = convo.question.toLowerCase().replace(/\s+/g, '').split('').filter(char => /\w/.test(char)); // 简单的分词
            let score = 0;
            keywords.forEach(kw => {
                if (normalizedMsg.includes(kw)) {
                    score++;
                }
            });
            if (score > bestMatch.score) {
                bestMatch = { score, reply: convo.answer };
            }
        }

        if (bestMatch.score > 2) { // 匹配阈值，可以根据需要调整
            addStatusMonitorEvent(`匹配到智能体知识: ${bestMatch.reply.substring(0,20)}...`, 'info');
            return bestMatch.reply;
        }

        return null;
    }, '智能体知识匹配');


    let lastSentReplyContent = '';
    let lastSentReplyTimestamp = 0;

    const sendQuickReply = withErrorHandling(async function(reply, sessionId, messageId, isFollowUp = false) {
        if (!isAutoReplyEnabled) {
            addStatusMonitorEvent('自动回复已在发送前被禁用。', 'warning');
            return;
        }
        // ========== 恢复：AI思考状态检查 ==========
        if (aiThinking) {
            addStatusMonitorEvent('AI正在思考中，暂停发送回复。', 'warning');
            return;
        }

        const now = Date.now();
        if (reply === lastSentReplyContent && (now - lastSentReplyTimestamp < 2000)) {
            addStatusMonitorEvent('重复消息，已阻止发送。', 'warning');
            return;
        }

        const inputField = findInputField();
        if (!inputField) {
            addStatusMonitorEvent('未找到输入框，无法发送。', 'error');
            throw new Error("未找到输入框。");
        }

        try {
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, "value").set;
            nativeInputValueSetter.call(inputField, reply);
            inputField.dispatchEvent(new Event('input', { bubbles: true, composed: true }));
            inputField.dispatchEvent(new Event('change', { bubbles: true, composed: true }));

            lastSentReplyContent = reply;
            lastSentMessageContent = reply;
            lastSentReplyTimestamp = now;

            await new Promise(r => setTimeout(r, 200));

            const sendButton = findSendButton();
            if (sendButton && !sendButton.disabled && !sendButton.classList.contains('chat-send__button__disabled')) {
                sendButton.click();
            } else {
                const enterEvent = new KeyboardEvent('keydown', { key: 'Enter', code: 'Enter', keyCode: 13, bubbles: true });
                inputField.dispatchEvent(enterEvent);
            }

            addStatusMonitorEvent(`已将消息[${reply.substring(0,20)}...]发送到对话框`, 'success');

            if(!isFollowUp){
                finalizeReply(reply, sessionId, messageId);
            }

        } catch (error) {
            SecureLogger.error(`发送回复失败: ${error.message}`);
            addStatusMonitorEvent('发送回复失败', 'error');
            if (lastSentReplyContent === reply && lastSentReplyTimestamp === now) {
                lastSentReplyTimestamp = 0;
            }
            throw error;
        }
    }, '快速回复发送');


    const finalizeReply = withErrorHandling(function(reply, sessionId, messageId) {
        todayReplies++;
        lastReplyTime = new Date().toLocaleTimeString();
        saveData();
        updateStats(document.getElementById('replyCount'), document.getElementById('lastReplyTime'));

        updateUserSessionList(sessionId, null, 'completed');

        if (monitoredSessions.has(sessionId)) {
            monitoredSessions.get(sessionId).status = 'replied';
        }

        // ========== 恢复：AI回复定时器清理 ==========
        if (aiReplyTimers.has(messageId)) {
            aiReplyTimers.delete(messageId);
            addStatusMonitorEvent(`AI回复定时器已清理 (ID: ${messageId})`, 'info');
        }
    }, '回复后处理');

    const findInputField = withErrorHandling(function() {
        const selectors = [
            '.edit-area',
            '#editArea',
            'textarea.text-area[data-v-4db39e81]',
            '#textInput', '.text-area textarea', '.chat-input textarea', 'textarea#input-area',
            'textarea[placeholder*="输入消息"]', 'textarea[placeholder*="回复"]', 'textarea[placeholder*="Type a message"]',
            'div[contenteditable="true"][aria-label*="message"]', 'div[contenteditable="true"][role="textbox"]',
            '.input-field', '.message-input', '.chat-input', 'textarea', 'div[contenteditable="true"]'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && !element.disabled && element.offsetParent !== null &&
                window.getComputedStyle(element).visibility !== 'hidden') {
                return element;
            }
        }

        return null;
    }, '输入框查找');

    const findSendButton = withErrorHandling(function() {
        const selectors = [
            '.btn.btn_primary',
            'button.send-btn-enable',
            '.btn-send', 'button[title*="发送"]', 'button[aria-label*="发送"]',
            'button[aria-label*="Send"]', 'button:has(svg[class*="send"])',
            'button:has(svg[d*="M2.01"])', '.chat-send', '.message-send',
            'button[type="submit"]', '.send-btn'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && !element.disabled && element.offsetParent !== null &&
                window.getComputedStyle(element).visibility !== 'hidden' &&
                !element.classList.contains('chat-send__button__disabled')
               ) {
                return element;
            }
        }

        const buttons = document.querySelectorAll('button, a');
        for (const btn of buttons) {
            const textContent = (btn.textContent || "").trim();
            if (textContent === "发送" || textContent === "Send") {
                if (!btn.disabled && btn.offsetParent !== null &&
                    window.getComputedStyle(btn).visibility !== 'hidden' &&
                    !btn.classList.contains('chat-send__button__disabled') &&
                    !btn.closest('.weui-desktop-btn_disabled')
                   ) {
                    return btn;
                }
            }
        }

        return null;
    }, '发送按钮查找');

    const getShippingInfoFromCard = withErrorHandling(async function() {
        addStatusMonitorEvent('尝试从收货信息卡片获取姓名和电话...', 'info');
        await new Promise(resolve => setTimeout(resolve, 500));
        const cards = document.querySelectorAll('.card');
        let shippingCard = null;
        for (const card of cards) {
            const label = card.querySelector('.label');
            if (label && label.textContent.includes('收货信息')) {
                shippingCard = card;
                break;
            }
        }
        if (shippingCard) {
            const valueDiv = shippingCard.querySelector('.value');
            if (valueDiv) {
                const text = valueDiv.textContent.trim().replace(/\s+/g, '，');
                const pattern = /^([^，]+)，(\d{3}\*{4}\d{4})/;
                const match = text.match(pattern);

                if (match) {
                    const name = match[1];
                    const phone = match[2];
                    addStatusMonitorEvent(`成功提取收货信息：姓名[${name}], 电话[${phone}]`, 'success');
                    return { name, phone };
                } else {
                     addStatusMonitorEvent(`收货信息格式不匹配，无法提取。内容: "${text}"`, 'warning');
                }
            }
        } else {
             addStatusMonitorEvent('未找到收货信息卡片。', 'info');
        }
        return null;
    }, '提取收货信息');


    const handleDamageReport = withErrorHandling(async function(message, sessionId, messageId) {
        const keywords = ['坏', '掉了', '破', '掉钻', '损坏', '破损', '碎了', '补发', '质量问题'];
        const normalizedMsg = message.toLowerCase().replace(/\s+/g, '');

        if (!keywords.some(kw => normalizedMsg.includes(kw))) {
            return false;
        }
        const activeSessionItem = document.querySelector('.session-item.active, .conversation-item.active, .session-item-container__active');
        const userNickname = getSessionName(activeSessionItem);
        if (!userNickname) {
            addStatusMonitorEvent('无法获取用户昵称，跳过反馈登记。', 'warning');
            return false;
        }

        if (feedbackRegistry.has(userNickname)) {
            addStatusMonitorEvent(`用户 ${userNickname} 的反馈已登记，跳过重复登记。`, 'info');
            const reassuringMessage = "亲，我们已经收到了您反馈的问题，正在加急为您处理中，请您耐心等待一下结果哦~";
            await sendQuickReply(reassuringMessage, sessionId, messageId, true); // true表示不追问
            return true;
        }
        addStatusMonitorEvent(`AI识别到用户 ${userNickname} 的损坏/补发请求，开始登记。`, 'success');
        let extractedOrderId = '未识别';
        let extractedProductName = '未识别';

        const orderCard = document.querySelector('.order-card:not([style*="display: none"])');
        if (orderCard) {
            const orderNumEl = orderCard.querySelector('.order-id-wrap .num');
            if (orderNumEl) {
                extractedOrderId = orderNumEl.textContent.trim();
                addStatusMonitorEvent(`成功提取订单号: ${extractedOrderId}`, 'success');
            } else {
                addStatusMonitorEvent('在订单卡片中未找到订单号元素', 'warning');
            }

            const productEl = orderCard.querySelector('.order-product .content-wrap .title');
            if (productEl) {
                extractedProductName = productEl.textContent.trim();
                addStatusMonitorEvent(`成功提取产品名称: ${extractedProductName}`, 'success');
            } else {
                addStatusMonitorEvent('在订单卡片中未找到产品名称元素', 'warning');
            }
        } else {
            addStatusMonitorEvent('在当前会话中未找到订单卡片', 'info');
        }

        const feedbackEntry = {
            userNickname: userNickname,
            orderId: extractedOrderId,
            productName: extractedProductName,
            problemDescription: message,
            registrationTime: new Date().toLocaleString('zh-CN'),
            replacementArrangement: '待审核',
            replacementStatus: '',
            replacementTracking: '',
        };

        feedbackRegistry.set(userNickname, feedbackEntry);
        document.getElementById('feedbackRegistryCount').textContent = feedbackRegistry.size;
        saveData();
        const guidanceMessage = "亲，看到您反馈的问题了，请问具体是哪里出现了损坏呢？可以发张图片或者详细描述一下情况哦。我们先为您登记，等待审批通过后会第一时间告知您是否可以补发。";
        await sendQuickReply(guidanceMessage, sessionId, messageId, true); // true表示不追问
        return true;
    }, '处理损坏报告');

    const downloadFeedbackReport = withErrorHandling(function() {
        const notification = document.getElementById('notification');
        if (feedbackRegistry.size === 0) {
            showNotification(notification, "当前没有待处理的用户反馈登记。");
            return;
        }

        const dataForExcel = Array.from(feedbackRegistry.values()).map(entry => ({
            '用户昵称': entry.userNickname,
            '订单号': entry.orderId,
            '产品名称': entry.productName,
            '问题描述': entry.problemDescription,
            '登记时间': entry.registrationTime,
            '补发安排': entry.replacementArrangement,
            '补发状态': entry.replacementStatus,
            '补发快递单号': entry.replacementTracking,
        }));

        try {
            const ws = XLSX.utils.json_to_sheet(dataForExcel);
            ws['!cols'] = [
                { wch: 20 },
                { wch: 25 },
                { wch: 30 },
                { wch: 40 },
                { wch: 20 },
                { wch: 20 },
                { wch: 20 },
                { wch: 25 },
            ];
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "AI客服登记反馈");
            XLSX.writeFile(wb, "AI客服登记反馈.xlsx");
            showNotification(notification, `已成功下载 ${feedbackRegistry.size} 条反馈。`);
            addStatusMonitorEvent(`成功下载 ${feedbackRegistry.size} 条反馈登记`, 'success');

        } catch (error) {
            SecureLogger.error(`反馈表格下载失败: ${error.message}`);
            showNotification(notification, "下载反馈表格失败");
            addStatusMonitorEvent('下载反馈表格失败', 'error');
        }
    }, '下载反馈报告');

    const handleFeedbackUpload = withErrorHandling(function(e) {
        if (!e.target.files || e.target.files.length === 0) return;
        const file = e.target.files[0];
        const notification = document.getElementById('notification');

        if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
            const reader = new FileReader();
            reader.onload = function(event) {
                try {
                    const data = new Uint8Array(event.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);

                    if (jsonData.length > 0) {
                        feedbackRegistry.clear(); // 清空旧的，以表格为准
                        let updatedCount = 0;
                        jsonData.forEach(row => {
                            const nickname = row['用户昵称'];
                             if (nickname && (row['补发安排'] || "").trim() !== '') { // 只加载有处理结果的
                                feedbackRegistry.set(nickname, {
                                    userNickname: nickname,
                                    orderId: row['订单号'] || 'N/A',
                                    productName: row['产品名称'] || 'N/A',
                                    problemDescription: row['问题描述'] || 'N/A',
                                    registrationTime: row['登记时间'] || 'N/A',
                                    replacementArrangement: row['补发安排'] || '',
                                    replacementStatus: row['补发状态'] || '',
                                    replacementTracking: row['补发快递单号'] || '',
                                });
                                updatedCount++;
                            }
                        });

                        document.getElementById('feedbackRegistryCount').textContent = feedbackRegistry.size;
                        saveData();
                        showNotification(notification, `成功加载 ${updatedCount} 条待回复的用户处理结果。`);
                        addStatusMonitorEvent(`加载了 ${updatedCount} 条待回复的用户处理结果`, 'success');
                    } else {
                        showNotification(notification, "上传的Excel文件中无有效数据。");
                        addStatusMonitorEvent('上传的Excel文件中无有效数据', 'warning');
                    }
                } catch (error) {
                     SecureLogger.error(`反馈表格解析失败: ${error.message}`);
                     showNotification(notification, "解析反馈表格失败，请检查文件格式。");
                     addStatusMonitorEvent('解析反馈表格失败', 'error');
                }
            };
            reader.readAsArrayBuffer(file);
        } else {
             showNotification(notification, "请上传.xlsx或.xls格式的Excel文件");
             addStatusMonitorEvent('请上传Excel文件', 'warning');
        }
        e.target.value = '';
    }, '上传反馈处理');

    const replyToRegisteredUsers = withErrorHandling(async function() {
        if (isAutoChecking || isAutoEnding || isReplyingToFeedback || processingSession) {
            addStatusMonitorEvent('批量回复任务延迟：已有其他核心任务在运行。', 'warning');
            showNotification(document.getElementById('notification'), '有其他任务正在运行，请等待其完成后再试。');
            setTimeout(replyToRegisteredUsers, 5000);
            return;
        }

        const notification = document.getElementById('notification');
        if (feedbackRegistry.size === 0) {
            showNotification(notification, "没有已加载处理结果的用户需要回复。");
            return;
        }

        isReplyingToFeedback = true;
        const replyBtn = document.getElementById('replyToFeedbackUsersBtn');
        if (replyBtn) replyBtn.disabled = true;

        try {
            addStatusMonitorEvent(`启动批量回复流程，共 ${feedbackRegistry.size} 位用户待通知...`, 'info');
            showNotification(notification, `开始批量回复，共 ${feedbackRegistry.size} 位用户...`);

            const allTab = Array.from(document.querySelectorAll('.tab-list-item')).find(el => el.textContent.trim() === '全部');
            if (allTab) {
                allTab.click();
                addStatusMonitorEvent('已点击"全部"会话标签页', 'info');
                await new Promise(resolve => setTimeout(resolve, 2000));
            } else {
                addStatusMonitorEvent('未找到"全部"会话标签页，将尝试在当前列表查找', 'warning');
            }

            const allSessionItems = Array.from(document.querySelectorAll('.session-item, .conversation-item, .session-item-container'));
            const sessionMap = new Map();
            allSessionItems.forEach(item => {
                const nickname = getSessionName(item);
                if (nickname) {
                    sessionMap.set(nickname, item);
                }
            });

            const usersToReply = Array.from(feedbackRegistry.values());
            for (const [index, feedbackData] of usersToReply.entries()) {
                 const nickname = feedbackData.userNickname;
                 const sessionElement = sessionMap.get(nickname);

                 if (sessionElement) {
                     addStatusMonitorEvent(`[${index + 1}/${usersToReply.length}] 正在回复: ${nickname}`, 'info');
                     sessionElement.click();
                     await new Promise(resolve => setTimeout(resolve, 2000));

                     // AI回复生成功能已删除，需要手动回复
                     addStatusMonitorEvent(`用户 ${nickname} 需要手动回复处理结果`, 'warning');
                 } else {
                     addStatusMonitorEvent(`[${index + 1}/${usersToReply.length}] 在会话列表中未找到用户: ${nickname}，跳过。`, 'warning');
                 }
            }

            addStatusMonitorEvent(`批量回复全部完成，清除登记表...`, 'success');
            feedbackRegistry.clear();
            document.getElementById('feedbackRegistryCount').textContent = 0;
            saveData();

            addStatusMonitorEvent('任务完成，3秒后将自动刷新页面。', 'success');
            showNotification(notification, `回复流程全部完成，即将刷新页面。`);
            setTimeout(() => {
                location.reload();
            }, 3000);

        } catch (error) {
            addStatusMonitorEvent(`批量回复过程中发生严重错误: ${error.message}`, 'error');
            SecureLogger.error(`批量回复过程中发生严重错误: ${error.message}`);
        } finally {
            setTimeout(() => {
               isReplyingToFeedback = false;
               if (replyBtn) replyBtn.disabled = false;
            }, 5000);
        }
    }, '回复已登记用户');


    // =================================================================
    // ========== AI智能体训练对话框相关功能 (含撤回功能优化) ==========
    // =================================================================

    const openTrainingDialog = withErrorHandling(function() {
        const dialog = document.getElementById('aiTrainingDialog');
        if (dialog) {
            dialog.style.display = 'flex';
            renderTrainingDialogContent();
        }
    }, '打开训练对话框');

    const closeTrainingDialog = withErrorHandling(function() {
        const dialog = document.getElementById('aiTrainingDialog');
        if (dialog) dialog.style.display = 'none';
    }, '关闭训练对话框');

    const renderTrainingDialogContent = withErrorHandling(function() {
        renderTrainingMessages();
        renderAgentKnowledgeSources();
    },'渲染训练对话框');

    const renderTrainingMessages = withErrorHandling(function() {
        const messagesContainer = document.getElementById('aiTrainingMessages');
        if (!messagesContainer) return;
        messagesContainer.innerHTML = trainingDialogMessages.map((msg, index) => `
            <div class="ai-training-msg ${msg.role}" data-index="${index}">
                <div class="avatar">${msg.role === 'user' ? '你' : 'AI'}</div>
                <div class="content">${msg.content.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</div>
            </div>
        `).join('');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }, '渲染训练消息');

    const handleRetractMessage = withErrorHandling(function(index) {
        const userMessage = trainingDialogMessages[index];
        if (!userMessage || userMessage.role !== 'user') {
            SecureLogger.warn('撤回失败：目标消息不是用户消息或索引无效。');
            return;
        }

        const userMessageContent = userMessage.content;

        const originalLength = aiAgentKnowledgeBase.conversations.length;
        aiAgentKnowledgeBase.conversations = aiAgentKnowledgeBase.conversations.filter(
            conv => conv.question !== userMessageContent && conv.answer !== userMessageContent
        );
        const removedCount = originalLength - aiAgentKnowledgeBase.conversations.length;

        trainingDialogMessages.splice(index, 2);

        saveData();
        renderTrainingDialogContent();

        const notification = document.getElementById('notification');
        showNotification(notification, `消息已撤回，并移除了 ${removedCount} 条相关知识。`);
        addStatusMonitorEvent('AI训练消息已撤回', 'info');

    }, '处理AI训练消息撤回');

    const removeRetractPopup = () => {
        const existingPopup = document.querySelector('.retract-popup');
        if (existingPopup) {
            existingPopup.remove();
        }
    };

    const renderAgentKnowledgeSources = withErrorHandling(function() {
        // 由于已删除文档知识和网页知识功能，此函数现在为空
        // 保留函数定义以避免其他地方的调用出错
    }, '渲染AI知识源');

    const initTrainingDialog = withErrorHandling(function() {
        const dialog = document.getElementById('aiTrainingDialog');
        const header = document.getElementById('aiTrainingDialogHeader');
        const closeBtn = document.getElementById('closeTrainingDialogBtn');
        const searchToggleBtn = document.getElementById('searchToggleBtn');
        const searchBar = document.getElementById('searchBar');
        const searchInput = document.getElementById('searchInput');
        const searchPrevBtn = document.getElementById('searchPrevBtn');
        const searchNextBtn = document.getElementById('searchNextBtn');
        const searchCloseBtn = document.getElementById('searchCloseBtn');
        const searchResultsSpan = document.getElementById('searchResults');
        const sendBtn = document.getElementById('sendTrainingMsgBtn');
        const input = document.getElementById('trainingInput');
        const uploadBtn = document.getElementById('uploadFileBtn');
        const fileInput = document.getElementById('trainingFileInput');
        const filesPreview = document.getElementById('uploadedFilesPreview');
        const qaTrainingBtn = document.getElementById('qaTrainingBtn'); // 【新增】问答训练按钮

        const messagesContainer = document.getElementById('aiTrainingMessages');

        if (!dialog) return;

        closeBtn.addEventListener('click', closeTrainingDialog);

        // 搜索功能事件监听器
        searchToggleBtn.addEventListener('click', toggleSearchBar);
        searchCloseBtn.addEventListener('click', closeSearchBar);
        searchInput.addEventListener('input', handleSearchInput);
        searchInput.addEventListener('keydown', handleSearchKeydown);
        searchPrevBtn.addEventListener('click', searchPrevious);
        searchNextBtn.addEventListener('click', searchNext);

        // 文件上传按钮点击事件
        uploadBtn.addEventListener('click', () => {
            fileInput.click();
        });

        // 文件选择事件
        fileInput.addEventListener('change', handleTrainingFileUpload);

        // 【新增】问答训练按钮事件
        if (qaTrainingBtn) {
            qaTrainingBtn.addEventListener('click', () => {
                const qaTemplate = "问题：\n\n回答：\n";
                input.value = qaTemplate;
                input.focus();
                // 设置光标位置到"问题："后面
                setTimeout(() => {
                    input.setSelectionRange(3, 3);
                    adjustTextareaHeight(input); // 调整高度
                }, 0);
            });
        }

        // 【新增】输入框自适应高度功能
        const adjustTextareaHeight = (textarea) => {
            if (!textarea) return;

            const minHeight = 50; // 最小高度
            const maxHeight = 144; // 最大高度（6行）

            // 【优化】如果输入框为空，直接重置到最小高度
            if (!textarea.value.trim()) {
                textarea.style.height = minHeight + 'px';
                textarea.style.overflowY = 'hidden';
                return;
            }

            // 重置高度以获取正确的scrollHeight
            textarea.style.height = 'auto';

            const scrollHeight = textarea.scrollHeight;
            const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);

            textarea.style.height = newHeight + 'px';

            // 如果内容超过最大高度，显示滚动条
            if (scrollHeight > maxHeight) {
                textarea.style.overflowY = 'auto';
            } else {
                textarea.style.overflowY = 'hidden';
            }
        };

        // 【新增】输入框内容变化时自动调整高度
        if (input) {
            input.addEventListener('input', () => {
                adjustTextareaHeight(input);
            });

            // 初始化时也调整一次高度
            adjustTextareaHeight(input);
        }

        const sendTrainingMessage = () => {
            const text = input.value.trim();
            const hasFiles = uploadedFiles.length > 0;

            if (text || hasFiles) {
                let messageContent = text;

                // 如果有上传的文件，添加文件信息到消息中
                if (hasFiles) {
                    const fileInfo = uploadedFiles.map(file => `[文件: ${file.name}]`).join(' ');
                    messageContent = text ? `${text}\n${fileInfo}` : fileInfo;
                }

                trainingDialogMessages.push({ role: 'user', content: messageContent });

                // 处理文件内容和文本
                const result = parseAndLearnWithFiles(text, uploadedFiles);
                trainingDialogMessages.push({ role: 'bot', content: result.message });

                input.value = '';
                clearUploadedFiles();
                // 【优化】发送后重置输入框高度到默认大小
                adjustTextareaHeight(input);
                saveData();
                renderTrainingDialogContent();
            }
        };
        sendBtn.addEventListener('click', sendTrainingMessage);
        input.addEventListener('keydown', (e) => {
            // 【优化】支持快捷键换行：Shift+Enter（Windows/Mac通用）
            if (e.key === 'Enter') {
                if (e.shiftKey) {
                    // Shift+Enter：换行，不发送消息
                    // 让默认行为执行（插入换行符）
                    setTimeout(() => {
                        adjustTextareaHeight(input); // 调整高度
                    }, 0);
                } else {
                    // 单独Enter：发送消息
                    e.preventDefault();
                    sendTrainingMessage();
                }
            }
        });

        if (messagesContainer) {
            messagesContainer.addEventListener('click', (e) => {
                const messageContent = e.target.closest('.ai-training-msg.user .content');
                if (messageContent) {
                    e.stopPropagation();
                    removeRetractPopup();

                    const messageWrapper = messageContent.closest('.ai-training-msg.user');
                    const index = parseInt(messageWrapper.dataset.index, 10);

                    const popup = document.createElement('div');
                    popup.className = 'retract-popup';
                    popup.textContent = '撤回';

                    popup.addEventListener('click', (event) => {
                        event.stopPropagation();
                        handleRetractMessage(index);
                        removeRetractPopup();
                    });

                    const rect = messageContent.getBoundingClientRect();
                    messagesContainer.appendChild(popup);
                    popup.style.left = `${messageContent.offsetLeft - popup.offsetWidth - 10}px`;
                    popup.style.top = `${messageContent.offsetTop + (messageContent.offsetHeight / 2) - (popup.offsetHeight / 2)}px`;
                }
            });
        }

        document.addEventListener('click', () => {
            removeRetractPopup();
        });
        dialog.addEventListener('click', e => e.stopPropagation());



        header.addEventListener('mousedown', (e) => {
            if (e.target.id === 'closeTrainingDialogBtn') return;
            isTrainingDialogDragging = true;
            dragOffsetX = e.clientX - dialog.offsetLeft;
            dragOffsetY = e.clientY - dialog.offsetTop;
            document.body.style.userSelect = 'none';
        });

        document.addEventListener('mousemove', (e) => {
            if (isTrainingDialogDragging) {
                dialog.style.left = `${e.clientX - dragOffsetX}px`;
                dialog.style.top = `${e.clientY - dragOffsetY}px`;
            }
        });

        document.addEventListener('mouseup', () => {
            isTrainingDialogDragging = false;
            document.body.style.userSelect = '';
        });
    }, '初始化训练对话框');

    const parseAndLearn = withErrorHandling(function(text) {
        const triggerMatch = text.match(/触发词[:：](.*)/);
        const selectorMatch = text.match(/数据获取代码[:：](.*)/);
        const templateMatch = text.match(/回复模板[:：](.*)/);

        if (triggerMatch && selectorMatch && templateMatch) {
            const triggers_raw = triggerMatch[1].trim();
            const selector_raw = selectorMatch[1].trim();
            const template_raw = templateMatch[1].trim();

            if (!triggers_raw || !selector_raw || !template_raw) {
                return { success: false, message: '学习失败：指令格式不完整，触发词、代码、模板均不能为空。' };
            }

            const selectorRegex = /document\.querySelector$['"](.*)[']$\.innerText/;
            const match = selector_raw.match(selectorRegex);
            if (!match) {
                 return { success: false, message: '学习失败：[数据获取代码]格式不安全或不支持。\n目前仅支持格式：\ndocument.querySelector(\'CSS选择器\').innerText' };
            }
            const selector = match[1];

            const newRule = {
                trigger_keywords: triggers_raw.split(/[/|、，,]/).map(k => k.trim()).filter(Boolean),
                selector: selector,
                template: template_raw,
            };
            aiAgentKnowledgeBase.dynamic_rules.push(newRule);
            addStatusMonitorEvent('学习了新的动态回复规则', 'success');
            return { success: true, message: '学习成功！已掌握新的动态回复规则。' };
        }

        let lastUserQuestion = null;
        for (let i = trainingDialogMessages.length - 2; i >= 0; i--) {
            if (trainingDialogMessages[i].role === 'user') {
                lastUserQuestion = trainingDialogMessages[i].content;
                break;
            }
        }

        if (lastUserQuestion) {
            aiAgentKnowledgeBase.conversations.push({
                question: lastUserQuestion,
                answer: text,
            });
            addStatusMonitorEvent('学习了新的问答对', 'success');
            return { success: true, message: '学习成功！已记住这个问答。' };
        } else {
             return { success: false, message: '我已记下您的问题，请现在输入对应的回答，我将把它们关联起来进行学习。' };
        }
    }, '解析和学习AI训练输入');

    // ========== 新增：文件上传和处理功能 ==========

    const handleTrainingFileUpload = withErrorHandling(async function(event) {
        const files = Array.from(event.target.files);
        if (files.length === 0) return;

        const filesPreview = document.getElementById('uploadedFilesPreview');

        for (const file of files) {
            // 检查文件大小（限制为10MB）
            if (file.size > 10 * 1024 * 1024) {
                showNotification(document.getElementById('notification'), `文件 ${file.name} 超过10MB限制，已跳过`);
                continue;
            }

            const fileId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const fileInfo = {
                id: fileId,
                name: file.name,
                type: file.type,
                size: file.size,
                file: file,
                content: null,
                processed: false
            };

            uploadedFiles.push(fileInfo);
            renderFilePreview(fileInfo);

            // 异步处理文件内容
            processFileContent(fileInfo);
        }

        // 清空文件输入
        event.target.value = '';
    }, '处理训练文件上传');

    const renderFilePreview = withErrorHandling(function(fileInfo) {
        const filesPreview = document.getElementById('uploadedFilesPreview');
        if (!filesPreview) return;

        const fileItem = document.createElement('div');
        fileItem.className = 'file-preview-item';
        fileItem.setAttribute('data-file-id', fileInfo.id);

        const icon = getFileIcon(fileInfo.type, fileInfo.name);

        // 创建文件图标
        const fileIconSpan = document.createElement('span');
        fileIconSpan.className = 'file-icon';
        fileIconSpan.textContent = icon;

        // 创建文件名
        const fileNameSpan = document.createElement('span');
        fileNameSpan.className = 'file-name';
        fileNameSpan.title = fileInfo.name;
        fileNameSpan.textContent = fileInfo.name;

        // 创建删除按钮
        const removeSpan = document.createElement('span');
        removeSpan.className = 'file-remove';
        removeSpan.textContent = '×';
        removeSpan.style.cursor = 'pointer';

        // 使用事件监听器而不是onclick属性
        removeSpan.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            removeUploadedFile(fileInfo.id);
        });

        // 组装元素
        fileItem.appendChild(fileIconSpan);
        fileItem.appendChild(fileNameSpan);
        fileItem.appendChild(removeSpan);

        filesPreview.appendChild(fileItem);
    }, '渲染文件预览');

    const getFileIcon = function(fileType, fileName) {
        if (fileType.startsWith('image/')) return '🖼️';
        if (fileType.startsWith('video/')) return '🎥';
        if (fileType.includes('pdf')) return '📄';
        if (fileType.includes('word') || fileName.endsWith('.doc') || fileName.endsWith('.docx')) return '📝';
        if (fileType.includes('excel') || fileName.endsWith('.xls') || fileName.endsWith('.xlsx')) return '📊';
        if (fileType.startsWith('text/')) return '📃';
        return '📎';
    };

    // 文件删除函数
    const removeUploadedFile = withErrorHandling(function(fileId) {
        console.log('删除文件:', fileId); // 调试日志

        // 从数组中移除文件
        const originalLength = uploadedFiles.length;
        uploadedFiles = uploadedFiles.filter(file => file.id !== fileId);

        console.log(`文件数组长度从 ${originalLength} 变为 ${uploadedFiles.length}`); // 调试日志

        // 从DOM中移除文件预览元素
        const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
        if (fileItem) {
            fileItem.remove();
            console.log('已从DOM中移除文件预览元素'); // 调试日志

            // 显示删除成功的通知
            showNotification(document.getElementById('notification'), `文件已删除`);
            addStatusMonitorEvent(`已删除上传文件: ${fileId}`, 'info');
        } else {
            console.error('未找到要删除的文件预览元素:', fileId); // 调试日志
        }
    }, '移除上传文件');

    // 将函数暴露到全局作用域，以便在HTML中调用（保持向后兼容）
    window.removeUploadedFile = removeUploadedFile;

    const clearUploadedFiles = withErrorHandling(function() {
        uploadedFiles = [];
        const filesPreview = document.getElementById('uploadedFilesPreview');
        if (filesPreview) {
            filesPreview.innerHTML = '';
        }
    }, '清空上传文件');

    const processFileContent = withErrorHandling(async function(fileInfo) {
        const fileItem = document.querySelector(`[data-file-id="${fileInfo.id}"]`);
        if (fileItem) {
            fileItem.classList.add('file-processing');
        }

        try {
            let content = '';

            if (fileInfo.type.startsWith('image/')) {
                content = await processImageFile(fileInfo.file);
            } else if (fileInfo.type.startsWith('video/')) {
                content = await processVideoFile(fileInfo.file);
            } else if (fileInfo.type.includes('pdf')) {
                content = await processPDFFile(fileInfo.file);
            } else if (fileInfo.type.includes('word') || fileInfo.name.endsWith('.doc') || fileInfo.name.endsWith('.docx')) {
                content = await processWordFile(fileInfo.file);
            } else if (fileInfo.type.includes('excel') || fileInfo.name.endsWith('.xls') || fileInfo.name.endsWith('.xlsx')) {
                content = await processExcelFile(fileInfo.file);
            } else if (fileInfo.type.startsWith('text/')) {
                content = await processTextFile(fileInfo.file);
            }

            fileInfo.content = content;
            fileInfo.processed = true;

            if (fileItem) {
                fileItem.classList.remove('file-processing');
            }

            addStatusMonitorEvent(`文件 ${fileInfo.name} 处理完成`, 'success');
        } catch (error) {
            SecureLogger.error(`处理文件 ${fileInfo.name} 失败: ${error.message}`);
            addStatusMonitorEvent(`文件 ${fileInfo.name} 处理失败: ${error.message}`, 'error');

            if (fileItem) {
                fileItem.classList.remove('file-processing');
                fileItem.style.opacity = '0.5';
            }
        }
    }, '处理文件内容');

    // ========== 各种文件类型的处理函数 ==========

    const processTextFile = withErrorHandling(async function(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('读取文本文件失败'));
            reader.readAsText(file, 'UTF-8');
        });
    }, '处理文本文件');

    const processImageFile = withErrorHandling(async function(file) {
        // 对于图片文件，我们可以提取基本信息
        // 在实际应用中，这里可以集成OCR服务来提取图片中的文字
        return `[图片文件: ${file.name}, 大小: ${(file.size / 1024).toFixed(2)}KB, 类型: ${file.type}]
注意：图片内容识别功能需要集成OCR服务，当前版本仅记录文件信息。`;
    }, '处理图片文件');

    const processVideoFile = withErrorHandling(async function(file) {
        // 对于视频文件，我们可以提取基本信息
        // 在实际应用中，这里可以集成视频分析服务来提取视频中的内容
        return `[视频文件: ${file.name}, 大小: ${(file.size / 1024 / 1024).toFixed(2)}MB, 类型: ${file.type}]
注意：视频内容识别功能需要集成视频分析服务，当前版本仅记录文件信息。`;
    }, '处理视频文件');

    const processWordFile = withErrorHandling(async function(file) {
        if (typeof mammoth === 'undefined') {
            throw new Error('Mammoth库未加载，无法处理Word文档');
        }

        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    const arrayBuffer = e.target.result;
                    const result = await mammoth.extractRawText({ arrayBuffer });
                    resolve(result.value || '无法提取文档内容');
                } catch (error) {
                    reject(new Error(`处理Word文档失败: ${error.message}`));
                }
            };
            reader.onerror = () => reject(new Error('读取Word文档失败'));
            reader.readAsArrayBuffer(file);
        });
    }, '处理Word文档');

    const processExcelFile = withErrorHandling(async function(file) {
        if (typeof XLSX === 'undefined') {
            throw new Error('XLSX库未加载，无法处理Excel文件');
        }

        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });

                    let content = '';
                    workbook.SheetNames.forEach((sheetName, index) => {
                        const worksheet = workbook.Sheets[sheetName];
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                        content += `\n=== 工作表: ${sheetName} ===\n`;
                        jsonData.forEach((row, rowIndex) => {
                            if (row.length > 0) {
                                content += `第${rowIndex + 1}行: ${row.join(' | ')}\n`;
                            }
                        });
                    });

                    resolve(content || '无法提取Excel内容');
                } catch (error) {
                    reject(new Error(`处理Excel文件失败: ${error.message}`));
                }
            };
            reader.onerror = () => reject(new Error('读取Excel文件失败'));
            reader.readAsArrayBuffer(file);
        });
    }, '处理Excel文件');

    const processPDFFile = withErrorHandling(async function(file) {
        // PDF处理需要额外的库，这里提供基本信息
        // 在实际应用中，可以集成PDF.js或其他PDF处理库
        return `[PDF文件: ${file.name}, 大小: ${(file.size / 1024).toFixed(2)}KB]
注意：PDF内容提取功能需要集成PDF处理库，当前版本仅记录文件信息。
建议：可以将PDF转换为文本文件后再上传，或者手动输入PDF中的关键内容。`;
    }, '处理PDF文件');

    const parseAndLearnWithFiles = withErrorHandling(function(text, files) {
        let combinedContent = text;
        let learnedCount = 0;

        // 将所有已处理的文件内容合并
        files.forEach(file => {
            if (file.processed && file.content) {
                combinedContent += `\n\n[来自文件 ${file.name}]:\n${file.content}`;
            }
        });

        // 如果有文件内容，智能学习文件内容
        if (files.length > 0) {
            files.forEach(file => {
                if (file.processed && file.content) {
                    const analysisResult = analyzeFileContent(file);

                    // 根据分析结果，以不同方式存储知识
                    if (analysisResult.type === 'qa_pairs') {
                        // 如果识别出问答对，分别存储
                        analysisResult.pairs.forEach(pair => {
                            aiAgentKnowledgeBase.conversations.push({
                                question: pair.question,
                                answer: pair.answer
                            });
                            learnedCount++;
                        });
                    } else if (analysisResult.type === 'structured_data') {
                        // 如果是结构化数据（如Excel），按类别存储
                        analysisResult.categories.forEach(category => {
                            aiAgentKnowledgeBase.conversations.push({
                                question: `关于${category.name}的信息`,
                                answer: category.content
                            });
                            learnedCount++;
                        });
                    } else {
                        // 默认存储方式
                        aiAgentKnowledgeBase.conversations.push({
                            question: `关于文件 ${file.name} 的内容`,
                            answer: file.content
                        });
                        learnedCount++;
                    }
                }
            });

            addStatusMonitorEvent(`智能学习了 ${files.length} 个文件，提取了 ${learnedCount} 条知识`, 'success');

            let message = `已成功智能学习 ${files.length} 个文件的内容！`;
            if (learnedCount > files.length) {
                message += `\n从文件中提取了 ${learnedCount} 条结构化知识。`;
            }
            if (text) {
                message += '\n同时也学习了您输入的文本内容。';
                // 如果有文本内容，也进行学习
                const textResult = parseAndLearn(text);
                if (textResult.success) {
                    learnedCount++;
                }
            }

            return {
                success: true,
                message: message
            };
        }

        // 如果没有文件，使用原来的文本学习逻辑
        return parseAndLearn(combinedContent);
    }, '解析和学习文件内容');

    const analyzeFileContent = withErrorHandling(function(fileInfo) {
        const content = fileInfo.content;
        const fileName = fileInfo.name;
        const fileType = fileInfo.type;

        // 分析Excel文件内容
        if (fileType.includes('excel') || fileName.endsWith('.xls') || fileName.endsWith('.xlsx')) {
            return analyzeExcelContent(content);
        }

        // 分析Word文档内容
        if (fileType.includes('word') || fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
            return analyzeWordContent(content);
        }

        // 分析文本文件内容
        if (fileType.startsWith('text/') || fileName.endsWith('.txt')) {
            return analyzeTextContent(content);
        }

        // 默认分析
        return {
            type: 'general',
            content: content
        };
    }, '分析文件内容');

    const analyzeExcelContent = withErrorHandling(function(content) {
        const categories = [];
        const lines = content.split('\n');
        let currentCategory = null;
        let currentContent = '';

        lines.forEach(line => {
            if (line.startsWith('=== 工作表:')) {
                if (currentCategory) {
                    categories.push({
                        name: currentCategory,
                        content: currentContent.trim()
                    });
                }
                currentCategory = line.replace('=== 工作表:', '').replace('===', '').trim();
                currentContent = '';
            } else if (line.trim()) {
                currentContent += line + '\n';
            }
        });

        if (currentCategory && currentContent) {
            categories.push({
                name: currentCategory,
                content: currentContent.trim()
            });
        }

        return {
            type: 'structured_data',
            categories: categories
        };
    }, '分析Excel内容');

    const analyzeWordContent = withErrorHandling(function(content) {
        // 尝试识别问答对格式
        const qaPairs = [];
        const lines = content.split('\n');

        for (let i = 0; i < lines.length - 1; i++) {
            const line = lines[i].trim();
            const nextLine = lines[i + 1].trim();

            // 识别问答格式：问：xxx 答：xxx
            if (line.includes('问：') || line.includes('Q:') || line.includes('问题：')) {
                const question = line.replace(/^(问：|Q:|问题：)/, '').trim();
                if (nextLine.includes('答：') || nextLine.includes('A:') || nextLine.includes('回答：')) {
                    const answer = nextLine.replace(/^(答：|A:|回答：)/, '').trim();
                    qaPairs.push({ question, answer });
                    i++; // 跳过下一行
                }
            }
        }

        if (qaPairs.length > 0) {
            return {
                type: 'qa_pairs',
                pairs: qaPairs
            };
        }

        return {
            type: 'general',
            content: content
        };
    }, '分析Word内容');

    const analyzeTextContent = withErrorHandling(function(content) {
        // 类似Word文档的分析逻辑
        return analyzeWordContent(content);
    }, '分析文本内容');

    // ========== 新增：搜索功能实现 ==========

    const toggleSearchBar = withErrorHandling(function() {
        const searchBar = document.getElementById('searchBar');
        const searchInput = document.getElementById('searchInput');

        if (searchBar.style.display === 'none' || !searchBar.style.display) {
            searchBar.style.display = 'block';
            searchInput.focus();
        } else {
            closeSearchBar();
        }
    }, '切换搜索栏');

    const closeSearchBar = withErrorHandling(function() {
        const searchBar = document.getElementById('searchBar');
        const searchInput = document.getElementById('searchInput');

        searchBar.style.display = 'none';
        searchInput.value = '';
        clearSearchHighlights();
        searchResults = [];
        currentSearchIndex = -1;
        searchQuery = '';
        updateSearchResults();
    }, '关闭搜索栏');

    const handleSearchInput = withErrorHandling(function(event) {
        const query = event.target.value.trim();
        if (query !== searchQuery) {
            searchQuery = query;
            if (query) {
                performSearch(query);
            } else {
                clearSearchHighlights();
                searchResults = [];
                currentSearchIndex = -1;
                updateSearchResults();
            }
        }
    }, '处理搜索输入');

    const handleSearchKeydown = withErrorHandling(function(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            if (event.shiftKey) {
                searchPrevious();
            } else {
                searchNext();
            }
        } else if (event.key === 'Escape') {
            closeSearchBar();
        }
    }, '处理搜索快捷键');

    const performSearch = withErrorHandling(function(query) {
        clearSearchHighlights();
        searchResults = [];
        currentSearchIndex = -1;

        if (!query) {
            updateSearchResults();
            return;
        }

        const messagesContainer = document.getElementById('aiTrainingMessages');
        const messages = messagesContainer.querySelectorAll('.ai-training-msg .content');

        messages.forEach((messageElement, messageIndex) => {
            // 先保存原始文本内容
            const originalText = messageElement.textContent;
            const regex = new RegExp(escapeRegExp(query), 'gi');
            let match;
            let highlightedHTML = originalText;
            let offset = 0;

            // 重置正则表达式的lastIndex
            regex.lastIndex = 0;

            while ((match = regex.exec(originalText)) !== null) {
                const matchStart = match.index + offset;
                const matchEnd = matchStart + query.length;
                const beforeMatch = highlightedHTML.substring(0, matchStart);
                const matchText = highlightedHTML.substring(matchStart, matchEnd);
                const afterMatch = highlightedHTML.substring(matchEnd);

                const highlightId = `search-highlight-${searchResults.length}`;
                highlightedHTML = beforeMatch +
                    `<span class="search-highlight" id="${highlightId}">${matchText}</span>` +
                    afterMatch;

                searchResults.push({
                    element: messageElement,
                    highlightId: highlightId,
                    messageIndex: messageIndex,
                    originalText: originalText,
                    matchIndex: match.index
                });

                offset += `<span class="search-highlight" id="${highlightId}"></span>`.length - query.length;

                // 防止无限循环，手动推进lastIndex
                if (match.index === regex.lastIndex) {
                    regex.lastIndex++;
                }
            }

            if (highlightedHTML !== originalText) {
                messageElement.innerHTML = highlightedHTML;
            }
        });

        updateSearchResults();
        if (searchResults.length > 0) {
            currentSearchIndex = 0;
            // 立即定位到第一个搜索结果，增加延迟确保DOM完全更新
            setTimeout(() => {
                highlightCurrentResult();
            }, 200); // 增加延迟时间
        }
    }, '执行搜索');

    const searchNext = withErrorHandling(function() {
        if (searchResults.length === 0) return;

        // 循环到下一个结果
        currentSearchIndex = (currentSearchIndex + 1) % searchResults.length;

        // 立即高亮并定位到新的结果
        setTimeout(() => {
            highlightCurrentResult();
        }, 100); // 增加延迟确保DOM更新
    }, '搜索下一个');

    const searchPrevious = withErrorHandling(function() {
        if (searchResults.length === 0) return;

        // 循环到上一个结果
        currentSearchIndex = currentSearchIndex <= 0 ? searchResults.length - 1 : currentSearchIndex - 1;

        // 立即高亮并定位到新的结果
        setTimeout(() => {
            highlightCurrentResult();
        }, 100); // 增加延迟确保DOM更新
    }, '搜索上一个');

    const highlightCurrentResult = withErrorHandling(function() {
        if (searchResults.length === 0 || currentSearchIndex < 0 || currentSearchIndex >= searchResults.length) {
            return;
        }

        // 移除之前的当前高亮
        document.querySelectorAll('.search-highlight.current').forEach(el => {
            el.classList.remove('current');
        });

        // 添加当前高亮
        const currentResult = searchResults[currentSearchIndex];
        const highlightElement = document.getElementById(currentResult.highlightId);

        if (highlightElement) {
            highlightElement.classList.add('current');

            // 确保元素可见并滚动到正确位置
            const messagesContainer = document.getElementById('aiTrainingMessages');

            if (messagesContainer) {
                // 使用 scrollIntoView 方法，这是最可靠的滚动定位方式
                highlightElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });

                // 备用方法：如果 scrollIntoView 不工作，使用计算方式
                setTimeout(() => {
                    // 获取元素相对于容器的位置
                    const containerRect = messagesContainer.getBoundingClientRect();
                    const elementRect = highlightElement.getBoundingClientRect();

                    // 检查元素是否在可视区域内
                    const isVisible = (
                        elementRect.top >= containerRect.top &&
                        elementRect.bottom <= containerRect.bottom
                    );

                    // 如果不在可视区域，手动滚动
                    if (!isVisible) {
                        // 计算相对位置
                        let elementTop = 0;
                        let element = highlightElement;

                        // 累计计算到容器的距离
                        while (element && element !== messagesContainer) {
                            elementTop += element.offsetTop;
                            element = element.offsetParent;
                        }

                        // 计算目标滚动位置
                        const containerHeight = messagesContainer.clientHeight;
                        const elementHeight = highlightElement.offsetHeight;
                        const targetScrollTop = elementTop - (containerHeight / 2) + (elementHeight / 2);

                        // 确保滚动位置在有效范围内
                        const maxScrollTop = messagesContainer.scrollHeight - containerHeight;
                        const finalScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));

                        messagesContainer.scrollTop = finalScrollTop;
                    }
                }, 100);

                // 添加视觉反馈效果
                highlightElement.style.transition = 'all 0.3s ease';
                highlightElement.style.transform = 'scale(1.05)';
                highlightElement.style.boxShadow = '0 0 10px rgba(255, 152, 0, 0.8)';

                setTimeout(() => {
                    highlightElement.style.transform = 'scale(1)';
                    highlightElement.style.boxShadow = '';
                }, 300);
            }
        }

        updateSearchResults();
    }, '高亮当前搜索结果');

    const clearSearchHighlights = withErrorHandling(function() {
        const messagesContainer = document.getElementById('aiTrainingMessages');
        const messages = messagesContainer.querySelectorAll('.ai-training-msg .content');

        messages.forEach(messageElement => {
            // 移除所有高亮标签，恢复原始文本
            const highlightElements = messageElement.querySelectorAll('.search-highlight');
            highlightElements.forEach(highlight => {
                const parent = highlight.parentNode;
                parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                parent.normalize(); // 合并相邻的文本节点
            });
        });
    }, '清除搜索高亮');

    const updateSearchResults = withErrorHandling(function() {
        const searchResultsSpan = document.getElementById('searchResults');
        const searchPrevBtn = document.getElementById('searchPrevBtn');
        const searchNextBtn = document.getElementById('searchNextBtn');

        if (searchResults.length === 0) {
            searchResultsSpan.textContent = '0/0';
            searchPrevBtn.disabled = true;
            searchNextBtn.disabled = true;
        } else {
            searchResultsSpan.textContent = `${currentSearchIndex + 1}/${searchResults.length}`;
            searchPrevBtn.disabled = false;
            searchNextBtn.disabled = false;
        }
    }, '更新搜索结果显示');

    const escapeRegExp = function(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    };







    // =================================================================
    // ========== 【新增】会话转接功能 =================================
    // =================================================================

    const populateAgentsFromCache = withErrorHandling(function() {
        const agentSelect = document.getElementById('transferToAgentSelect');
        if (!agentSelect) return false;

        if (availableAgents.length > 0) {
            agentSelect.innerHTML = '<option value="">--请选择客服--</option>';
            availableAgents.forEach(name => {
                const option = document.createElement('option');
                option.value = name;
                option.textContent = name;
                agentSelect.appendChild(option);
            });
            agentSelect.value = transferToAgent; // 恢复之前的选择
            agentSelect.disabled = false;
            addStatusMonitorEvent(`已从缓存加载 ${availableAgents.length} 位客服`, 'info');
            return true;
        }
        return false;
    }, '从缓存填充客服列表');

    const initTransferFeature = withErrorHandling(function() {
        const keywordsInput = document.getElementById('transferKeywordsInput');
        const agentSelect = document.getElementById('transferToAgentSelect');
        const refreshBtn = document.getElementById('refreshAgentsBtn');

        if (keywordsInput && agentSelect && refreshBtn) {
            keywordsInput.value = transferKeywords;
            agentSelect.value = transferToAgent;

            keywordsInput.addEventListener('change', (e) => {
                transferKeywords = e.target.value.trim();
                GM_setValue('transferKeywords', transferKeywords);
                addStatusMonitorEvent('转接关键词已保存', 'info');
                saveData();
            });

            agentSelect.addEventListener('change', (e) => {
                transferToAgent = e.target.value;
                GM_setValue('transferToAgent', transferToAgent);
                addStatusMonitorEvent(`转接客服已设置为: ${transferToAgent}`, 'info');
                saveData();
            });

            refreshBtn.addEventListener('click', fetchAndPopulateAgents);

            // 【修复】只在没有缓存数据时才自动获取，否则使用缓存数据
            if (!populateAgentsFromCache()) {
                addStatusMonitorEvent('未找到客服列表缓存，将在首次使用时获取', 'info');
                agentSelect.innerHTML = '<option value="">--请选择客服--</option>';
                agentSelect.disabled = false;
            }
        }
    }, '初始化会话转接功能');

    const fetchAndPopulateAgents = withErrorHandling(async function() {
        addStatusMonitorEvent('正在获取客服列表...', 'info');
        const agentSelect = document.getElementById('transferToAgentSelect');
        if (!agentSelect) return;

        agentSelect.innerHTML = '<option value="">正在加载...</option>';
        agentSelect.disabled = true;

        try {
            // 1. 点击左侧 "人员" 按钮
            const personnelTab = Array.from(document.querySelectorAll('li.menu-list-tab .name')).find(el => el.textContent.trim() === '人员');
            if (!personnelTab) {
                throw new Error('未找到 "人员" 标签页按钮');
            }
            personnelTab.closest('li').click();
            addStatusMonitorEvent('已点击 "人员" 按钮，等待页面加载...', 'info');
            await new Promise(r => setTimeout(r, 2000)); // 等待人员列表加载

            // 2. 提取客服名称 - 修复选择器，使用正确的元素路径
            // 根据HTML结构，客服名称在 .head-img-right 元素中
            let agentNameElements = document.querySelectorAll('.head-img .head-img-right');
            if (agentNameElements.length === 0) {
                // 如果第一个选择器失败，尝试备用选择器
                agentNameElements = document.querySelectorAll('.ant-table-tbody .nickname, .weui-desktop-table__core-table .head-img-right');
                if (agentNameElements.length === 0) {
                    throw new Error('在 "人员" 页面未找到任何客服名称，请检查页面是否正确加载');
                }
            }

            availableAgents = Array.from(agentNameElements).map(el => el.textContent.trim()).filter(Boolean);
            if (availableAgents.length === 0) {
                throw new Error('提取到的客服名称列表为空');
            }
            addStatusMonitorEvent(`成功获取 ${availableAgents.length} 位客服: ${availableAgents.join(', ')}`, 'success');

            // 3. 填充下拉框
            agentSelect.innerHTML = '<option value="">--请选择客服--</option>';
            availableAgents.forEach(name => {
                const option = document.createElement('option');
                option.value = name;
                option.textContent = name;
                agentSelect.appendChild(option);
            });

            agentSelect.value = transferToAgent; // 恢复之前的选择
            agentSelect.disabled = false;

            // 【新增】保存客服列表到缓存
            saveData();

        } catch (error) {
            SecureLogger.error('获取客服列表失败');
            addStatusMonitorEvent(`获取客服列表失败`, 'error');
            agentSelect.innerHTML = '<option value="">获取失败</option>';
        } finally {
            // 4. 无论成功失败，都尝试切回 "会话" 页面
            const chatTab = Array.from(document.querySelectorAll('li.menu-list-tab .name')).find(el => el.textContent.trim() === '会话');
             if (chatTab) {
                 chatTab.closest('li').click();
                 addStatusMonitorEvent('已自动切回 "会话" 页面', 'info');
             }
        }
    }, '获取并填充客服列表');

    const checkForAndHandleTransfer = withErrorHandling(async function(message) {
        if (!transferKeywords || !transferToAgent) {
            return false; // 如果未设置，则跳过
        }

        const keywords = transferKeywords.split('/').map(k => k.trim().toLowerCase()).filter(Boolean);
        const normalizedMsg = message.toLowerCase();

        const isTriggered = keywords.some(kw => normalizedMsg.includes(kw));

        if (isTriggered) {
            addStatusMonitorEvent(`消息触发转接关键词，准备转接给 ${transferToAgent}...`, 'warning');
            await executeTransfer(transferToAgent);
            return true; // 表示已处理
        }

        return false;
    }, '检查并处理自动转接');

    const executeTransfer = withErrorHandling(async function(agentName) {
        try {
            // 1. 点击主转接按钮
            const mainTransferButton = await waitForElementFast('.chat-title .func-wrap.bold', { textContent: '转接' });
            mainTransferButton.click();
            addStatusMonitorEvent('已点击主转接按钮', 'info');

            // 2. 等待转接弹窗出现，并在弹窗内找到目标客服
            const transferPopup = await waitForElementFast('.transfer-popover2');

            // 【优化】使用更快速的元素查找，减少等待时间
            let agentItem = null;
            let attempts = 0;
            const maxAttempts = 20; // 最多尝试20次，每次50ms，总共1秒

            while (!agentItem && attempts < maxAttempts) {
                // 优先使用 .head-img-right，备用 .nickname
                const agentElements = transferPopup.querySelectorAll('.head-img-right, .item .nickname');
                agentItem = Array.from(agentElements).find(el =>
                    el.textContent && el.textContent.trim().includes(agentName)
                );

                if (!agentItem) {
                    await new Promise(r => setTimeout(r, 50)); // 快速重试
                    attempts++;
                }
            }

            if (!agentItem) {
                throw new Error(`在转接弹窗中未找到客服: ${agentName}`);
            }

            // 点击客服项目（可能需要点击父元素）
            const clickableElement = agentItem.closest('.item') || agentItem.closest('tr') || agentItem;
            clickableElement.click();
            addStatusMonitorEvent(`已在弹窗中选择客服: ${agentName}`, 'info');

            // 【优化】减少等待时间，快速查找转接按钮
            await new Promise(r => setTimeout(r, 100)); // 减少等待时间

            // 3. 点击弹窗内的最终 "转接" 按钮
            const finalTransferButton = await waitForElementFast('.transfer-popover2 button.weui-desktop-btn_primary:not(.weui-desktop-btn_disabled)', {
                textContent: '转接',
                timeout: 2000 // 减少超时时间
            });
            finalTransferButton.click();
            addStatusMonitorEvent(`成功将当前会话转接给 ${agentName}`, 'success');

            // 转接后，当前会话通常会消失，可以触发一次刷新来更新列表
            setTimeout(() => location.reload(), 1500); // 减少刷新等待时间

        } catch (error) {
            SecureLogger.error('自动转接流程失败');
            addStatusMonitorEvent(`自动转接失败`, 'error');
        }
    }, '执行自动转接');


    // =================================================================
    // ========== 脚本主流程和初始化 ==========
    // =================================================================

    // 主初始化函数
    function init() {
        loadSavedData();

        // 根据当前URL执行不同的逻辑
        if (window.location.href.includes('/shop/kf') || window.location.href.includes('/shop/home')) {
            window.addEventListener('storage', handleStorageChange);
            for(let i=localStorage.length -1; i>=0; i--){
                const key = localStorage.key(i);
                if(key.startsWith(TASK_STORAGE_KEY_PREFIX)){
                    localStorage.removeItem(key);
                }
            }
            setTimeout(initControlPanel, 500);
        } else if (window.location.href.includes('/shop/order/list')) {
            runAutoQueryTaskInNewTab();
        } else {
             setTimeout(initControlPanel, 500);
        }
    }

    init();

    let lastUrl = location.href;
    const observer = new MutationObserver(() => {
        const currentUrl = location.href;
        if (currentUrl !== lastUrl) {
            lastUrl = currentUrl;
            loadSavedData();
            isAutoChecking = false;
            isAutoEnding = false;
            isReplyingToFeedback = false;
            aiThinking = false; // 恢复：AI思考状态重置
            processingSession = false;
            if(processingSessionTimeout) clearTimeout(processingSessionTimeout);
            addStatusMonitorEvent('检测到页面切换，重置任务状态并重新初始化...', 'info');
            setTimeout(initControlPanel, 1500); // 延迟初始化以确保页面元素加载完毕
        }
    });
    observer.observe(document.body, { childList: true, subtree: true });

    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.shiftKey && e.key === 'P') {
            e.preventDefault();
            const panel = document.getElementById('autoReplyPanel');
            const thumbnail = document.getElementById('collapsedThumbnail');
            if (panel) {
                if (panel.style.display === 'none') {
                    // 【优化】展开控制面板
                    panel.style.display = 'flex';
                    if (thumbnail) thumbnail.style.display = 'none';
                    addStatusMonitorEvent('控制面板已展开 (快捷键)', 'info');
                } else {
                    // 【优化】收起控制面板和所有相关窗口
                    panel.style.display = 'none';

                    // 【新增】同时收起所有相关窗口
                    const aiTrainingDialog = document.getElementById('aiTrainingDialog');
                    const logHistoryModal = document.getElementById('logHistoryModal');
                    const shopProductsModal = document.getElementById('shopProductsModal');

                    if (aiTrainingDialog) aiTrainingDialog.style.display = 'none';
                    if (logHistoryModal) logHistoryModal.style.display = 'none';
                    if (shopProductsModal) shopProductsModal.style.display = 'none';

                    addStatusMonitorEvent('所有面板窗口已收起 (快捷键)', 'info');
                }
            }
        }
    });

    setInterval(() => {
        const panel = document.getElementById('autoReplyPanel');
        const modal = document.getElementById('logHistoryModal');
        const trainingDialog = document.getElementById('aiTrainingDialog');
        if (!panel && !modal) {
            addStatusMonitorEvent('控制面板丢失，重新初始化...', 'warning');
            initControlPanel();
        } else if (!trainingDialog) {
            document.body.insertAdjacentHTML('beforeend', aiTrainingDialogHTML);
            initTrainingDialog();
        } else if (!panel || panel.style.display === 'none') {
            // Check if user is actively using the page to avoid popping up unexpectedly
        }
    }, 5000);


    // 安全启动信息 - 不再输出到console，防止代码泄露
    SecureLogger.info('小梅花智能AI客服 (v9.0.21) 已加载');
    SecureLogger.info('提示：按 Ctrl+Shift+P 可以显示/隐藏控制面板');
    SecureLogger.info('反调试功能已启动，保护脚本安全运行');
    addStatusMonitorEvent('脚本已加载 (v9.0.21)', 'success');
    addStatusMonitorEvent('反调试保护已启用', 'info');
})();